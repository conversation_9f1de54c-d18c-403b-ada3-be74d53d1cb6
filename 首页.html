<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <title>笔尖传奇写作</title>
    <style>
        /* --- 1. 全局与动态配色系统 (与简约版、专业版统一) --- */
        :root {
            /* 默认皮肤 (护眼蓝灰) */
            --bg-main: #F0F3F7;
            --bg-panel: #FAFBFC;
            --bg-panel-secondary: #F3F5F8;
            --bg-content: #F7F9FB;
            --text-dark: #2D3748;
            --text-light: #718096;
            --text-on-primary: #FFFFFF;
            --border-color: #E8EBEF;
            --gutter-color: transparent;
            --shadow-color: rgba(93, 156, 236, 0.08);
            --shadow-color-light: rgba(0, 0, 0, 0.04);
            --shadow-color-heavy: rgba(0, 0, 0, 0.12);
            /* 主题色 */
            --primary-color: #5D9CEC;
            --primary-color-hover: #4A89E2;
            --primary-color-light: #7AABF0;
            --secondary-color: #8696A7;
            --accent-color: #48BB78;
            --accent-color-hover: #3AA967;
            --warning-color: #FF6B6B;
            --warning-color-hover: #FF5252;
            /* 内容区专用色 */
            --content-header-bg: #E8F2FF;
            --content-header-color: #2B5797;
            /* 布局尺寸 */
            --font-size-base: 16px;
            --font-size-sm: 14px;
            --font-size-lg: 18px;
            --header-height: 52px;
            --sidebar-width: 80px;
            --sidebar-collapsed-width: 4px;
        }

        [data-theme="green-leaf"] {
            --bg-main: #EFF3EF;
            --bg-panel: #F8FAF8;
            --bg-panel-secondary: #F0F3F0;
            --bg-content: #F5F8F5;
            --text-dark: #3E4A3E;
            --text-light: #6B7C6B;
            --border-color: #E0E6E0;
            --primary-color: #6A9C89;
            --primary-color-hover: #5A8C79;
            --primary-color-light: #7AAC99;
            --secondary-color: #8A9B94;
            --accent-color: #E99469;
            --accent-color-hover: #D98459;
            --shadow-color: rgba(106, 156, 137, 0.08);
            --content-header-bg: #E5F2E9;
            --content-header-color: #3A6B4F;
        }

        [data-theme="sepia"] {
            --bg-main: #FBF0D9;
            --bg-panel: #FAF4E8;
            --bg-panel-secondary: #F6ECDA;
            --bg-content: #FAF4E8;
            --text-dark: #5C4B33;
            --text-light: #8B7355;
            --border-color: #EAE0C8;
            --primary-color: #A67B5B;
            --primary-color-hover: #966B4B;
            --primary-color-light: #B68B6B;
            --secondary-color: #B0A08D;
            --accent-color: #5D9CEC;
            --accent-color-hover: #4A89E2;
            --shadow-color: rgba(166, 123, 91, 0.1);
            --content-header-bg: #F4E6D4;
            --content-header-color: #7A5A3A;
        }

        [data-theme="dark"] {
            --bg-main: #1A202C;
            --bg-panel: #2D3748;
            --bg-panel-secondary: #252E3E;
            --bg-content: #323B4C;
            --text-dark: #E2E8F0;
            --text-light: #A0AEC0;
            --border-color: #4A5568;
            --primary-color: #4A5568;
            --primary-color-hover: #718096;
            --primary-color-light: #5A6578;
            --secondary-color: #3B475C;
            --accent-color: #48BB78;
            --accent-color-hover: #3AA967;
            --shadow-color: rgba(0, 0, 0, 0.2);
            --content-header-bg: #3A4558;
            --content-header-color: #CBD5E0;
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", "Helvetica Neue", sans-serif;
            background-color: var(--bg-main);
            margin: 0;
            padding: 0;
            color: var(--text-dark);
            font-size: var(--font-size-base);
            line-height: 1.7;
            -webkit-font-smoothing: antialiased;
            display: flex;
            height: 100vh;
            overflow: hidden;
            transition: background-color 0.3s, color 0.3s;
        }

        /* --- 换肤按钮容器 --- */
        .theme-switcher {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            gap: 10px;
            background: var(--bg-panel);
            padding: 8px;
            border-radius: 12px;
            box-shadow: 0 2px 8px var(--shadow-color);
        }

        .theme-btn {
            width: 36px;
            height: 36px;
            border-radius: 8px;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.2s;
            position: relative;
            overflow: hidden;
        }

        .theme-btn:hover {
            transform: scale(1.1);
            border-color: var(--primary-color);
        }

        .theme-btn.active {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }

        .theme-btn.theme-default {
            background: linear-gradient(135deg, #F0F3F7 50%, #FAFBFC 50%);
        }

        .theme-btn.theme-green-leaf {
            background: linear-gradient(135deg, #EFF3EF 50%, #F8FAF8 50%);
        }

        .theme-btn.theme-sepia {
            background: linear-gradient(135deg, #FBF0D9 50%, #FAF4E8 50%);
        }

        .theme-btn.theme-dark {
            background: linear-gradient(135deg, #1A202C 50%, #2D3748 50%);
        }

        /* --- 左侧导航栏 (可自动隐藏) --- */
        .sidebar-wrapper {
            position: relative;
            width: var(--sidebar-width);
            flex-shrink: 0;
            transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar-wrapper.collapsed {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar {
            position: absolute;
            left: 0;
            top: 0;
            width: var(--sidebar-width);
            height: 100%;
            background: var(--bg-panel);
            box-shadow: 2px 0 8px var(--shadow-color);
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 0;
            z-index: 100;
            transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        /* 隐藏状态 */
        .sidebar-wrapper.collapsed .sidebar {
            transform: translateX(calc(-1 * var(--sidebar-width) + var(--sidebar-collapsed-width)));
        }

        /* 鼠标感应区域 - 修复：使用更大的热区 */
        .sidebar-trigger {
            position: fixed;
            left: 0;
            top: 0;
            width: 20px; /* 减小宽度，避免干扰内容 */
            height: 100%;
            z-index: 101;
        }

        /* 导航栏展开时的热区扩大 */
        .sidebar-wrapper:not(.collapsed) .sidebar-trigger {
            width: calc(var(--sidebar-width) + 20px);
        }

        /* 导航栏内容淡入淡出 */
        .sidebar-content {
            opacity: 1;
            transition: opacity 0.2s;
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
        }
        .sidebar-wrapper.collapsed .sidebar-content {
            opacity: 0;
            pointer-events: none;
        }

        /* 用户头像 - 调整大小 */
        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px var(--shadow-color);
            transition: transform 0.2s, border-color 0.3s, background-color 0.3s;
            flex-shrink: 0;
            cursor: pointer;
            border: 2px solid var(--primary-color);
            background: var(--primary-color);
        }
        .user-avatar:hover {
            transform: scale(1.05);
        }
        .user-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        /* 导航组 */
        .nav-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
            width: 100%;
        }

        /* 导航项 */
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            color: var(--text-light);
            transition: all 0.2s ease;
            padding: 8px;
            border-radius: 10px;
            width: 60px;
        }
        .nav-item:hover {
            color: var(--primary-color);
            background-color: var(--bg-panel-secondary);
        }
        .nav-item.active {
            color: var(--primary-color);
            background-color: var(--bg-panel-secondary);
        }
        .nav-item-icon {
            font-size: 20px;
            width: 24px;
            height: 24px;
            fill: currentColor;
        }
        .nav-item-text {
            font-size: 11px;
            font-weight: 500;
        }
        .sidebar-footer {
            margin-top: auto;
        }

        /* --- 主内容区 --- */
        .main-content {
            flex-grow: 1;
            height: 100vh;
            overflow-y: auto;
            transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* --- 首页 --- */
        .home-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100%;
            padding: 40px 20px;
            animation: fadeIn 0.8s ease-out;
        }
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .hero-section {
            text-align: center;
            background: var(--bg-panel);
            padding: 80px 60px;
            border-radius: 24px;
            box-shadow: 0 2px 8px var(--shadow-color);
            max-width: 900px;
            width: 100%;
            transition: background-color 0.3s;
        }
        .hero-title {
            font-size: 3.2rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 20px;
            letter-spacing: -1px;
        }
        .hero-subtitle {
            font-size: 1.25rem;
            color: var(--text-light);
            margin-bottom: 60px;
            font-weight: 400;
        }
        .version-buttons {
            display: flex;
            gap: 40px;
            justify-content: center;
            flex-wrap: wrap;
        }
        .version-card {
            background: var(--bg-content);
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 2px 8px var(--shadow-color);
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid var(--border-color);
            position: relative;
            overflow: hidden;
            flex: 1;
            min-width: 280px;
        }
        .version-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 10px 30px var(--shadow-color-heavy);
            border-color: var(--primary-color);
        }
        .version-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }
        .version-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-dark);
            margin-bottom: 15px;
        }
        .version-desc {
            color: var(--text-light);
            font-size: 1rem;
            line-height: 1.6;
            margin-bottom: 25px;
        }
        .version-features {
            text-align: left;
            font-size: var(--font-size-sm);
            color: var(--text-light);
        }
        .version-features li {
            list-style: none;
            padding: 5px 0;
            position: relative;
            padding-left: 20px;
        }
        .version-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--accent-color);
            font-weight: bold;
        }

        /* --- 通用配置弹窗样式 --- */
        .config-modal {
            display: none;
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;
            justify-content: center;
            align-items: center;
        }
        .config-content {
            background: var(--bg-panel);
            padding: 40px;
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            max-width: 700px;
            width: 90%;
            max-height: 85vh;
            overflow-y: auto;
        }
        .config-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .config-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        .config-step {
            font-size: var(--font-size-base);
            color: var(--text-light);
        }
        .config-section {
            margin-bottom: 30px;
            animation: slideUp 0.4s ease-out forwards;
            animation-delay: 0.1s;
        }
        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .config-label {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 15px;
            display: block;
        }

        /* 选项网格 */
        .option-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 15px;
            margin-bottom: 10px;
        }
        .grid-2-cols {
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
        }

        /* 选项卡片 */
        .option-card {
            background: var(--bg-panel-secondary);
            padding: 20px;
            border-radius: 12px;
            border: 2px solid var(--border-color);
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
        }
        .option-card:hover {
            border-color: var(--primary-color);
            background: var(--bg-content);
            transform: translateY(-2px);
        }
        .option-card.selected {
            border-color: var(--primary-color);
            background: var(--bg-content);
            position: relative;
        }
        .option-card.selected::after {
            content: '✓';
            position: absolute;
            top: 10px;
            right: 10px;
            color: var(--primary-color);
            font-weight: bold;
            font-size: 1.2rem;
        }

        .option-name {
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 5px;
            font-size: var(--font-size-base);
        }
        .option-desc {
            font-size: var(--font-size-sm);
            color: var(--text-light);
        }
        .option-icon {
            font-size: 2rem;
            margin-bottom: 10px;
            display: block;
        }

        /* 简约版专用 */
        .prompt-select {
            width: 100%;
            padding: 15px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: var(--font-size-base);
            background: var(--bg-panel);
            color: var(--text-dark);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .prompt-select:hover {
            border-color: var(--primary-color);
        }
        .prompt-select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }
        .tutorial-window {
            background: var(--bg-panel-secondary);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            padding: 20px;
            margin-top: 15px;
            max-height: 200px;
            overflow-y: auto;
        }
        .tutorial-title {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: var(--font-size-base);
        }
        .tutorial-content {
            font-size: var(--font-size-sm);
            color: var(--text-light);
            line-height: 1.6;
        }

        /* 通用按钮 */
        .config-actions {
            display: flex;
            gap: 15px;
            margin-top: 40px;
            justify-content: flex-end;
        }
        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 10px;
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn-primary {
            background: var(--primary-color);
            color: var(--text-on-primary);
        }
        .btn-primary:hover {
            background: var(--primary-color-hover);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px var(--shadow-color-heavy);
        }
        .btn-primary:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .btn-secondary {
            background: var(--bg-panel);
            color: var(--primary-color);
            border: 1px solid var(--border-color);
        }
        .btn-secondary:hover {
            background: var(--primary-color);
            color: var(--text-on-primary);
            border-color: var(--primary-color);
        }

        /* --- 创作界面 --- (框架搭建) */
        .creation-interface {
            display: none;
            grid-template-columns: 280px 1fr 350px;
            height: 100%;
            width: 100%;
        }
        .creation-panel {
            background: var(--bg-panel);
            height: 100%;
            overflow-y: auto;
            padding: 24px;
            border-right: 1px solid var(--border-color);
        }
        .creation-panel:last-child {
            border-right: none;
        }
        .panel-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
            padding-bottom: 16px;
            margin-bottom: 16px;
            border-bottom: 1px solid var(--border-color);
        }
        .editor-panel {
            background: var(--bg-content);
        }

        /* 用于文件上传的隐藏input */
        #fileUploader {
            display: none;
        }

        /* --- 个人中心弹窗样式 --- */
        .user-center-modal {
            display: none;
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1002;
            animation: fadeIn 0.3s ease-out;
            justify-content: center;
            align-items: center;
        }
        .user-center-content {
            background: var(--bg-panel);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            width: 320px;
            max-width: 90vw;
            overflow: hidden;
            animation: slideUp 0.4s ease-out;
            position: relative;
        }
        .user-center-header {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            padding: 30px 20px 20px;
            text-align: center;
            color: white;
            position: relative;
        }
        .user-center-avatar {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            overflow: hidden;
            margin: 0 auto 12px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            background: var(--primary-color);
            transition: background-color 0.3s ease;
        }
        .user-center-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .user-center-name {
            font-size: var(--font-size-lg);
            font-weight: 600;
            margin-bottom: 4px;
        }
        .user-center-id {
            font-size: var(--font-size-sm);
            opacity: 0.8;
            margin-bottom: 15px;
        }
        .user-center-balance {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 20px;
            padding: 8px 16px;
            font-size: var(--font-size-sm);
            display: inline-block;
            backdrop-filter: blur(10px);
        }
        .user-center-close {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }
        .user-center-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }
        .user-center-body {
            padding: 0;
        }
        .user-center-recharge {
            background: linear-gradient(135deg, #FF6B6B, #FF8E8E);
            color: white;
            border: none;
            padding: 15px 20px;
            width: 100%;
            font-size: var(--font-size-base);
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            position: relative;
            overflow: hidden;
        }
        .user-center-recharge:hover {
            background: linear-gradient(135deg, #FF5252, #FF7979);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }
        .user-center-recharge::before {
            content: '💰';
            margin-right: 8px;
        }
        .user-center-menu {
            padding: 8px 0;
        }
        .user-center-menu-item {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            color: var(--text-dark);
            text-decoration: none;
            transition: all 0.2s;
            cursor: pointer;
            border: none;
            background: none;
            width: 100%;
            text-align: left;
            font-size: var(--font-size-base);
            position: relative;
            overflow: hidden;
        }
        .user-center-menu-item:hover {
            background: var(--bg-panel-secondary);
            color: var(--primary-color);
            transform: translateX(4px);
        }
        .user-center-menu-item:hover .user-center-menu-item-icon {
            transform: scale(1.1);
        }
        .user-center-menu-item:hover .user-center-menu-item-arrow {
            transform: translateX(4px);
            color: var(--primary-color);
        }
        .user-center-menu-item:active {
            transform: translateX(2px);
        }
        .user-center-menu-item-icon {
            width: 20px;
            height: 20px;
            margin-right: 12px;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .user-center-menu-item-text {
            flex: 1;
        }
        .user-center-menu-item-arrow {
            color: var(--text-light);
            font-size: 12px;
        }
        .user-center-divider {
            height: 1px;
            background: var(--border-color);
            margin: 8px 0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .user-center-content, .recharge-full-content, .profile-content, .guide-content, .purchase-content, .prompt-gifts-content {
                width: 95vw;
                margin: 0 auto;
            }
            .package-grid {
                grid-template-columns: 1fr;
            }
            .payment-methods {
                flex-direction: column;
            }
            .guide-step {
                flex-direction: column;
                text-align: center;
            }
            .guide-step-number {
                margin: 0 auto 15px;
            }
            .prompt-gifts-item {
                padding: 12px 16px;
                margin-bottom: 6px;
            }
            .prompt-gifts-item-title {
                font-size: 14px;
                margin-bottom: 4px;
            }
            .prompt-gifts-item-meta {
                font-size: 12px;
            }
            .prompt-gifts-item-words {
                font-size: 14px;
            }
            .guide-actions {
                flex-direction: column;
                gap: 10px;
            }
            .guide-actions .btn {
                width: 100%;
                text-align: center;
            }
        }
        @media (max-width: 480px) {
            .user-center-header, .recharge-full-header, .profile-header, .guide-header, .purchase-header, .exchange-header, .prompt-gifts-header {
                padding: 20px 15px;
            }
            .user-center-avatar {
                width: 50px;
                height: 50px;
            }
            .user-center-name, .recharge-full-title, .profile-title, .guide-title, .purchase-title, .prompt-gifts-title {
                font-size: var(--font-size-base);
            }
            .guide-tooltip {
                left: 0;
                max-width: calc(100vw - 20px);
                border-radius: 0 12px 12px 0;
            }
            .prompt-gifts-item {
                padding: 10px 12px;
                margin-bottom: 4px;
            }
            .prompt-gifts-item-title {
                font-size: 13px;
                margin-bottom: 3px;
            }
            .prompt-gifts-item-meta {
                font-size: 11px;
            }
            .prompt-gifts-item-words {
                font-size: 13px;
            }
        }

        /* 动画优化 */
        .user-center-modal {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .user-center-modal.show {
            opacity: 1;
        }
        .user-center-content {
            transform: scale(0.9) translateY(20px);
            transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        .user-center-modal.show .user-center-content {
            transform: scale(1) translateY(0);
        }

        /* --- 充值弹窗样式 --- */
        .recharge-modal {
            display: none;
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1003;
            animation: fadeIn 0.3s ease-out;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .recharge-modal.show {
            opacity: 1;
        }
        .recharge-content {
            background: var(--bg-panel);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            width: 400px;
            max-width: 90vw;
            overflow: hidden;
            transform: scale(0.9) translateY(20px);
            transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        .recharge-modal.show .recharge-content {
            transform: scale(1) translateY(0);
        }
        .recharge-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
            padding: 25px 20px;
            text-align: center;
            color: white;
            position: relative;
            transition: background 0.3s ease;
        }
        .recharge-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            margin: 0;
        }
        .recharge-close {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }
        .recharge-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }
        .recharge-body {
            padding: 30px 20px;
            text-align: center;
        }
        .recharge-message {
            font-size: var(--font-size-base);
            color: var(--text-dark);
            margin-bottom: 20px;
            line-height: 1.6;
        }
        .recharge-actions {
            display: flex;
            gap: 12px;
            justify-content: center;
        }

        /* --- 完整充值界面样式 --- */
        .recharge-full-modal {
            display: none;
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1003;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .recharge-full-modal.show {
            opacity: 1;
        }
        .recharge-full-content {
            background: var(--bg-panel);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            width: 600px;
            max-width: 90vw;
            max-height: 80vh;
            overflow: hidden;
            transform: scale(0.9) translateY(20px);
            transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        .recharge-full-modal.show .recharge-full-content {
            transform: scale(1) translateY(0);
        }
        .recharge-full-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
            padding: 25px 30px;
            color: white;
            position: relative;
            display: flex;
            transition: background 0.3s ease;
            align-items: center;
            gap: 12px;
        }
        .recharge-full-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            margin: 0;
            flex: 1;
        }
        .recharge-full-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }
        .recharge-full-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }
        .recharge-full-body {
            padding: 30px;
            max-height: 60vh;
            overflow-y: auto;
        }
        .package-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .package-card {
            background: var(--bg-panel-secondary);
            border: 2px solid var(--border-color);
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .package-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-4px);
            box-shadow: 0 8px 25px var(--shadow-color-heavy);
        }
        .package-card.selected {
            border-color: var(--primary-color);
            background: var(--bg-content);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }
        .package-card.recommended::before {
            content: '推荐';
            position: absolute;
            top: 15px;
            right: -25px;
            background: var(--accent-color);
            color: white;
            padding: 4px 30px;
            font-size: 12px;
            transform: rotate(45deg);
            font-weight: 600;
        }
        .package-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
        }
        .package-price {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        .package-price .currency {
            font-size: 1.2rem;
            vertical-align: top;
        }
        .package-original-price {
            font-size: var(--font-size-sm);
            color: var(--text-light);
            text-decoration: line-through;
            margin-bottom: 15px;
        }
        .package-features {
            list-style: none;
            padding: 0;
            margin: 0;
            text-align: left;
        }
        .package-features li {
            padding: 6px 0;
            color: var(--text-dark);
            font-size: var(--font-size-sm);
            position: relative;
            padding-left: 20px;
        }
        .package-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--accent-color);
            font-weight: bold;
        }
        .payment-section {
            border-top: 1px solid var(--border-color);
            padding-top: 25px;
        }
        .payment-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 15px;
        }
        .payment-methods {
            display: flex;
            gap: 15px;
            margin-bottom: 25px;
        }
        .payment-method {
            flex: 1;
            padding: 15px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.2s;
        }
        .payment-method:hover {
            border-color: var(--primary-color);
        }
        .payment-method.selected {
            border-color: var(--primary-color);
            background: var(--bg-content);
        }
        .payment-method-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }
        .payment-method-name {
            font-size: var(--font-size-sm);
            color: var(--text-dark);
            font-weight: 500;
        }
        .recharge-full-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        /* 套餐类型切换标签 */
        .package-type-tabs {
            display: flex;
            background: var(--bg-panel-secondary);
            border-radius: 12px;
            padding: 4px;
            margin-bottom: 25px;
            gap: 4px;
        }
        .package-type-tab {
            flex: 1;
            padding: 12px 20px;
            border: none;
            background: transparent;
            color: var(--text-light);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: var(--font-size-sm);
            font-weight: 500;
        }
        .package-type-tab.active {
            background: var(--primary-color);
            color: white;
            box-shadow: 0 2px 8px var(--shadow-color);
        }
        .package-type-tab:hover:not(.active) {
            background: var(--bg-content);
            color: var(--text-dark);
        }

        /* --- 个人资料界面样式 --- */
        .profile-modal {
            display: none;
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1003;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .profile-modal.show {
            opacity: 1;
        }
        .profile-content {
            background: var(--bg-panel);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            width: 500px;
            max-width: 90vw;
            max-height: 80vh;
            overflow: hidden;
            transform: scale(0.9) translateY(20px);
            transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        .profile-modal.show .profile-content {
            transform: scale(1) translateY(0);
        }
        .profile-header {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            padding: 25px 30px;
            color: white;
            position: relative;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .profile-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            margin: 0;
            flex: 1;
        }
        .profile-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }
        .profile-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }
        .profile-body {
            padding: 30px;
            max-height: 60vh;
            overflow-y: auto;
        }
        .profile-avatar-section {
            text-align: center;
            margin-bottom: 30px;
        }
        .profile-avatar-large {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            overflow: hidden;
            margin: 0 auto 15px;
            border: 3px solid var(--primary-color);
            cursor: pointer;
            transition: all 0.2s;
            background: var(--primary-color);
        }
        .profile-avatar-large:hover {
            border-color: var(--primary-color);
            transform: scale(1.05);
        }
        .profile-avatar-large img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .profile-avatar-change {
            font-size: var(--font-size-sm);
            color: var(--primary-color);
            cursor: pointer;
            text-decoration: underline;
        }
        .profile-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }
        .profile-field {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .profile-label {
            font-size: var(--font-size-sm);
            font-weight: 600;
            color: var(--text-dark);
        }
        .profile-input {
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 10px;
            font-size: var(--font-size-base);
            background: var(--bg-panel-secondary);
            color: var(--text-dark);
            transition: all 0.2s;
        }
        .profile-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }
        .profile-textarea {
            min-height: 80px;
            resize: vertical;
        }
        .profile-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin-bottom: 20px;
        }
        .profile-stat {
            text-align: center;
            padding: 15px;
            background: var(--bg-panel-secondary);
            border-radius: 12px;
        }
        .profile-stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 4px;
        }
        .profile-stat-label {
            font-size: var(--font-size-sm);
            color: var(--text-light);
        }
        .profile-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 25px;
        }

        /* --- 新手引导界面样式 --- */
        .guide-modal {
            display: none;
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1003;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .guide-modal.show {
            opacity: 1;
        }
        .guide-content {
            background: var(--bg-panel);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            width: 700px;
            max-width: 90vw;
            max-height: 80vh;
            overflow: hidden;
            transform: scale(0.9) translateY(20px);
            transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        .guide-modal.show .guide-content {
            transform: scale(1) translateY(0);
        }
        .guide-header {
            background: linear-gradient(135deg, #48BB78, #68D391);
            padding: 25px 30px;
            color: white;
            position: relative;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .guide-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            margin: 0;
            flex: 1;
        }
        .guide-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }
        .guide-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }
        .guide-body {
            padding: 30px;
            max-height: 60vh;
            overflow-y: auto;
        }
        .guide-steps {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }
        .guide-step {
            display: flex;
            gap: 20px;
            padding: 20px;
            background: var(--bg-panel-secondary);
            border-radius: 16px;
            border-left: 4px solid var(--primary-color);
            transition: all 0.2s;
        }
        .guide-step:hover {
            background: var(--bg-content);
            transform: translateX(4px);
        }
        .guide-step-number {
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            flex-shrink: 0;
        }
        .guide-step-content {
            flex: 1;
        }
        .guide-step-title {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
        }
        .guide-step-desc {
            font-size: var(--font-size-sm);
            color: var(--text-light);
            line-height: 1.6;
        }
        .guide-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        /* --- 兑换码界面样式 --- */
        .exchange-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.3s;
        }
        .exchange-modal.show {
            opacity: 1;
        }
        .exchange-content {
            background: var(--bg-panel);
            border-radius: 20px;
            width: 90%;
            max-width: 500px;
            max-height: 80vh;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            transform: scale(0.9) translateY(20px);
            transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        .exchange-modal.show .exchange-content {
            transform: scale(1) translateY(0);
        }
        .exchange-header {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-color-light));
            padding: 25px 30px;
            color: white;
            position: relative;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background 0.3s ease;
        }
        .exchange-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            margin: 0;
        }
        .exchange-close {
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s;
        }
        .exchange-close:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }
        .exchange-body {
            padding: 30px;
            max-height: 60vh;
            overflow-y: auto;
        }
        .exchange-description {
            background: var(--bg-content);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            border: 1px solid var(--border-color);
        }
        .exchange-desc-title {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 10px;
        }
        .exchange-desc-text {
            font-size: var(--font-size-sm);
            color: var(--text-light);
            line-height: 1.6;
        }
        .exchange-form {
            margin-bottom: 30px;
        }
        .exchange-field {
            margin-bottom: 20px;
        }
        .exchange-label {
            display: block;
            font-size: var(--font-size-sm);
            font-weight: 500;
            color: var(--text-dark);
            margin-bottom: 8px;
        }
        .exchange-input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            font-size: var(--font-size-base);
            background: var(--bg-content);
            color: var(--text-dark);
            transition: border-color 0.2s;
            text-transform: uppercase;
        }
        .exchange-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }
        .exchange-input-hint {
            font-size: var(--font-size-xs);
            color: var(--text-light);
            margin-top: 5px;
        }
        .exchange-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }
        .exchange-history {
            border-top: 1px solid var(--border-color);
            padding-top: 25px;
        }
        .exchange-history-title {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 15px;
        }
        .exchange-history-list {
            max-height: 200px;
            overflow-y: auto;
        }
        .exchange-history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: var(--bg-content);
            border-radius: 8px;
            margin-bottom: 10px;
            border: 1px solid var(--border-color);
        }
        .exchange-history-info {
            flex: 1;
        }
        .exchange-history-code {
            font-size: var(--font-size-sm);
            font-weight: 600;
            color: var(--text-dark);
            font-family: 'Courier New', monospace;
        }
        .exchange-history-reward {
            font-size: var(--font-size-sm);
            color: var(--primary-color);
            margin: 2px 0;
        }
        .exchange-history-time {
            font-size: var(--font-size-xs);
            color: var(--text-light);
        }
        .exchange-history-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: var(--font-size-xs);
            font-weight: 500;
        }
        .exchange-history-status.success {
            background: rgba(102, 187, 106, 0.1);
            color: var(--success-color);
        }

        /* --- 字数赠送界面样式 --- */
        .prompt-gifts-modal {
            display: none;
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1003;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .prompt-gifts-modal.show {
            opacity: 1;
        }
        .prompt-gifts-content {
            background: var(--bg-panel);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            width: 600px;
            max-width: 90vw;
            max-height: 80vh;
            overflow: hidden;
            transform: scale(0.9) translateY(20px);
            transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        .prompt-gifts-modal.show .prompt-gifts-content {
            transform: scale(1) translateY(0);
        }
        .prompt-gifts-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 25px 30px;
            color: white;
            position: relative;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .prompt-gifts-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            margin: 0;
            flex: 1;
        }
        .prompt-gifts-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }
        .prompt-gifts-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }
        .prompt-gifts-body {
            padding: 30px;
            max-height: 60vh;
            overflow-y: auto;
        }
        .prompt-gifts-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .prompt-gifts-item {
            padding: 16px 20px;
            background: var(--bg-panel-secondary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            transition: all 0.2s;
            margin-bottom: 8px;
        }
        .prompt-gifts-item:hover {
            background: var(--bg-content);
            border-color: var(--primary-color);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px var(--shadow-color);
        }
        .prompt-gifts-item-title {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 6px;
            line-height: 1.4;
        }
        .prompt-gifts-item-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: var(--font-size-sm);
        }
        .prompt-gifts-item-date {
            color: var(--text-light);
            font-weight: 400;
        }
        .prompt-gifts-item-words {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--primary-color);
        }
        .prompt-gifts-empty {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-light);
        }
        .prompt-gifts-empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        .prompt-gifts-empty-text {
            font-size: var(--font-size-lg);
            margin-bottom: 10px;
        }
        .prompt-gifts-empty-desc {
            font-size: var(--font-size-sm);
        }
        .prompt-gifts-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        /* --- 购买记录界面样式 --- */
        .purchase-modal {
            display: none;
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1003;
            justify-content: center;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        .purchase-modal.show {
            opacity: 1;
        }
        .purchase-content {
            background: var(--bg-panel);
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            width: 600px;
            max-width: 90vw;
            max-height: 80vh;
            overflow: hidden;
            transform: scale(0.9) translateY(20px);
            transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
        }
        .purchase-modal.show .purchase-content {
            transform: scale(1) translateY(0);
        }
        .purchase-header {
            background: linear-gradient(135deg, #667eea, #764ba2);
            padding: 25px 30px;
            color: white;
            position: relative;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .purchase-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            margin: 0;
            flex: 1;
        }
        .purchase-close {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s;
        }
        .purchase-close:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }
        .purchase-body {
            padding: 30px;
            max-height: 60vh;
            overflow-y: auto;
        }
        .purchase-list {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        .purchase-item {
            padding: 20px;
            background: var(--bg-panel-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            transition: all 0.2s;
        }
        .purchase-item:hover {
            background: var(--bg-content);
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px var(--shadow-color);
        }
        .purchase-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .purchase-item-title {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--text-dark);
        }
        .purchase-item-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        .purchase-item-status.success {
            background: var(--accent-color);
            color: white;
        }
        .purchase-item-status.pending {
            background: var(--warning-color);
            color: white;
        }
        .purchase-item-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: var(--font-size-sm);
            color: var(--text-light);
        }
        .purchase-item-price {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--primary-color);
        }
        .purchase-empty {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-light);
        }
        .purchase-empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        .purchase-empty-text {
            font-size: var(--font-size-lg);
            margin-bottom: 10px;
        }
        .purchase-empty-desc {
            font-size: var(--font-size-sm);
        }
        .purchase-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }

        /* 主题适配 */
        [data-theme="dark"] .user-center-content {
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }
        [data-theme="dark"] .recharge-content {
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }
        [data-theme="sepia"] .user-center-header {
            background: linear-gradient(135deg, var(--primary-color), #D4A574);
        }

        [data-theme="green-leaf"] .user-center-header {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        }

        /* --- 引导提示样式 --- */
        .guide-tooltip {
            position: fixed;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            background: var(--bg-panel);
            border: 2px solid var(--primary-color);
            border-radius: 0 16px 16px 0;
            padding: 20px;
            box-shadow: 0 8px 32px var(--shadow-color-heavy);
            z-index: 999;
            max-width: 320px;
            opacity: 0;
            transform: translateY(-50%) translateX(-100%);
            transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
            pointer-events: none;
        }
        .guide-tooltip.show {
            opacity: 1;
            transform: translateY(-50%) translateX(0);
            pointer-events: auto;
        }
        .guide-tooltip-header {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
        }
        .guide-tooltip-icon {
            font-size: 24px;
            margin-right: 8px;
        }
        .guide-tooltip-title {
            font-size: var(--font-size-base);
            font-weight: 600;
            color: var(--primary-color);
            margin: 0;
        }
        .guide-tooltip-content {
            font-size: var(--font-size-sm);
            color: var(--text-dark);
            line-height: 1.5;
            margin-bottom: 15px;
        }
        .guide-tooltip-actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
        }
        .guide-tooltip-btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .guide-tooltip-btn.primary {
            background: var(--primary-color);
            color: white;
        }
        .guide-tooltip-btn.primary:hover {
            background: var(--primary-color-hover);
        }
        .guide-tooltip-btn.secondary {
            background: var(--bg-panel-secondary);
            color: var(--text-light);
        }
        .guide-tooltip-btn.secondary:hover {
            background: var(--border-color);
        }





        /* Toast 动画 - 左侧滑入 */
        @keyframes slideInLeft {
            from {
                transform: translateY(-50%) translateX(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(-50%) translateX(0);
                opacity: 1;
            }
        }
        @keyframes slideOutLeft {
            from {
                transform: translateY(-50%) translateX(0);
                opacity: 1;
            }
            to {
                transform: translateY(-50%) translateX(-100%);
                opacity: 0;
            }
        }

        /* 微交互动画 */
        .package-card, .payment-method, .guide-step, .purchase-item, .prompt-gifts-item, .profile-stat {
            animation: fadeInUp 0.4s ease-out forwards;
            opacity: 0;
            transform: translateY(20px);
        }
        .package-card:nth-child(1) { animation-delay: 0.1s; }
        .package-card:nth-child(2) { animation-delay: 0.2s; }
        .package-card:nth-child(3) { animation-delay: 0.3s; }
        .package-card:nth-child(4) { animation-delay: 0.4s; }
        .guide-step:nth-child(1) { animation-delay: 0.1s; }
        .guide-step:nth-child(2) { animation-delay: 0.2s; }
        .guide-step:nth-child(3) { animation-delay: 0.3s; }
        .guide-step:nth-child(4) { animation-delay: 0.4s; }
        .guide-step:nth-child(5) { animation-delay: 0.5s; }
        .guide-step:nth-child(6) { animation-delay: 0.6s; }
        .purchase-item:nth-child(1) { animation-delay: 0.1s; }
        .purchase-item:nth-child(2) { animation-delay: 0.2s; }
        .purchase-item:nth-child(3) { animation-delay: 0.3s; }
        .prompt-gifts-item:nth-child(1) { animation-delay: 0.1s; }
        .prompt-gifts-item:nth-child(2) { animation-delay: 0.2s; }
        .prompt-gifts-item:nth-child(3) { animation-delay: 0.3s; }
        .prompt-gifts-item:nth-child(4) { animation-delay: 0.4s; }
        .profile-stat:nth-child(1) { animation-delay: 0.1s; }
        .profile-stat:nth-child(2) { animation-delay: 0.2s; }
        .profile-stat:nth-child(3) { animation-delay: 0.3s; }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 按钮点击效果 */
        .btn:active, .user-center-menu-item:active, .package-card:active, .payment-method:active {
            transform: scale(0.98);
        }

        /* --- 书籍选择弹窗样式 --- */
        .book-selector-modal {
            display: none;
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.6);
            z-index: 1001;
            animation: fadeIn 0.3s ease-out;
            justify-content: center;
            align-items: center;
        }
        .book-selector-content {
            background: var(--bg-panel);
            padding: 40px;
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            max-width: 800px;
            width: 90%;
            max-height: 85vh;
            overflow-y: auto;
            animation: slideUp 0.4s ease-out;
        }
        .book-selector-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .book-selector-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }
        .book-selector-subtitle {
            font-size: var(--font-size-base);
            color: var(--text-light);
        }
        
        /* 搜索框 */
        .book-search-container {
            margin-bottom: 25px;
            position: relative;
        }
        .book-search-input {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            font-size: var(--font-size-base);
            background: var(--bg-panel-secondary);
            color: var(--text-dark);
            transition: all 0.2s ease;
        }
        .book-search-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }
        
        /* 书籍列表 */
        .book-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            max-height: 400px;
            overflow-y: auto;
            padding-right: 10px;
        }
        .book-item {
            background: var(--bg-panel-secondary);
            padding: 20px;
            border-radius: 16px;
            border: 2px solid var(--border-color);
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        .book-item:hover {
            border-color: var(--primary-color);
            background: var(--bg-content);
            transform: translateY(-4px);
            box-shadow: 0 8px 25px var(--shadow-color-heavy);
        }
        .book-item.selected {
            border-color: var(--primary-color);
            background: var(--bg-content);
            box-shadow: 0 0 0 3px var(--shadow-color);
        }
        .book-item.selected::after {
            content: '✓';
            position: absolute;
            top: 15px;
            right: 15px;
            color: var(--primary-color);
            font-weight: bold;
            font-size: 1.5rem;
            animation: checkmarkPop 0.3s ease-out;
        }
        @keyframes checkmarkPop {
            0% { transform: scale(0); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
        
        .book-cover {
            width: 60px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 8px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: white;
            flex-shrink: 0;
        }
        .book-info {
            flex-grow: 1;
        }
        .book-title {
            font-size: var(--font-size-lg);
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 8px;
            line-height: 1.3;
        }
        .book-meta {
            display: flex;
            flex-direction: column;
            gap: 4px;
            font-size: var(--font-size-sm);
            color: var(--text-light);
        }
        .book-type {
            display: inline-block;
            padding: 4px 8px;
            background: var(--primary-color);
            color: white;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            width: fit-content;
        }
        .book-stats {
            display: flex;
            gap: 15px;
            margin-top: 8px;
        }
        .book-stat {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        /* 空状态 */
        .book-empty-state {
            text-align: center;
            padding: 60px 20px;
            color: var(--text-light);
        }
        .book-empty-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        .book-empty-text {
            font-size: var(--font-size-lg);
            margin-bottom: 10px;
        }
        .book-empty-desc {
            font-size: var(--font-size-sm);
        }
        
        /* 按钮组 */
        .book-selector-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid var(--border-color);
        }
    </style>
</head>
<body>

    <!-- 引导提示 -->
    <div class="guide-tooltip" id="guideTooltip">
        <div class="guide-tooltip-header">
            <div class="guide-tooltip-icon">👋</div>
            <h4 class="guide-tooltip-title">欢迎使用笔尖传奇</h4>
        </div>
        <div class="guide-tooltip-content">
            将鼠标悬停在最左侧边缘，即可呼出导航栏，点击头像打开个人中心
        </div>
        <div class="guide-tooltip-actions">
            <button class="guide-tooltip-btn secondary" onclick="permanentlyCloseGuide()">永久关闭</button>
            <button class="guide-tooltip-btn secondary" onclick="skipGuide()">跳过</button>
            <button class="guide-tooltip-btn primary" onclick="startGuide()">开始体验</button>
        </div>
    </div>





    <!-- 换肤按钮 -->
    <div class="theme-switcher">
        <div class="theme-btn theme-default active" data-theme="default" title="护眼蓝灰"></div>
        <div class="theme-btn theme-green-leaf" data-theme="green-leaf" title="绿野仙踪"></div>
        <div class="theme-btn theme-sepia" data-theme="sepia" title="复古羊皮"></div>
        <div class="theme-btn theme-dark" data-theme="dark" title="深夜模式"></div>
    </div>

    <!-- 鼠标感应区域 -->
    <div class="sidebar-trigger" id="sidebarTrigger"></div>

    <!-- 左侧导航栏包装器 -->
    <div class="sidebar-wrapper collapsed" id="sidebarWrapper">
        <!-- 左侧导航栏 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <div class="user-avatar" onclick="openUserCenter()">
                    <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="南城">
                </div>
                <div class="nav-group">
                    <div class="nav-item active" title="首页">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                        </svg>
                        <span class="nav-item-text">首页</span>
                    </div>
				    <div class="nav-item" title="书架" onclick="window.location.href='书架.html'">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/>
                        </svg>
                        <span class="nav-item-text">书架</span>
                    </div>
                    <div class="nav-item" title="创意">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M14.06,9.02l0.91,0.91L5.66,19.24L4.75,18.33L14.06,9.02 M17.66,3c-0.25,0-0.51,0.1-0.7,0.29l-1.83,1.83l3.75,3.75l1.83-1.83c0.39-0.39,0.39-1.02,0-1.41l-2.34-2.34C18.17,3.09,17.92,3,17.66,3L17.66,3z M12.06,6.19L3,15.25V19.24h3.99l9.06-9.06L12.06,6.19z"/>
                        </svg>
                        <span class="nav-item-text">创意</span>
                    </div>
                    <div class="nav-item" title="对话">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20 2H4c-1.1 0-2 .9-2 2v18l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
                        </svg>
                        <span class="nav-item-text">对话</span>
                    </div>
                    <div class="nav-item" title="论坛" onclick="window.open('https://bbs.bijianchuanqi.com/', '_blank')">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 3c5.5 0 10 3.58 10 8s-4.5 8-10 8c-1.24 0-2.43-.18-3.53-.5L5 21l1.53-3.47C5.59 16.4 5 14.76 5 13c0-4.42 4.5-8 10-8z"/>
                            <path d="M9 2c3.87 0 7 2.51 7 5.6 0 3.09-3.13 5.6-7 5.6-.87 0-1.69-.13-2.47-.35L4 14l1.08-1.95C4.41 11.34 4 10.51 4 9.6 4 6.51 7.13 4 11 4z" opacity="0.7"/>
                            <circle cx="8" cy="9" r="1"/>
                            <circle cx="11" cy="9" r="1"/>
                            <circle cx="14" cy="9" r="1"/>
                        </svg>
                        <span class="nav-item-text">论坛</span>
                    </div>
                    <div class="nav-item" title="模拟">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm0-14c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z M12 4c1.66 0 3-1.34 3-3s-1.34-3-3-3-3 1.34-3 3 1.34 3 3 3z"/>
                        </svg>
                        <span class="nav-item-text">模拟</span>
                    </div>
                </div>
                <div class="sidebar-footer nav-group">
                    <div class="nav-item" title="教程" onclick="showGuideTooltip()">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M18 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM6 4h5v8l-2.5-1.5L6 12V4z"/>
                        </svg>
                        <span class="nav-item-text">教程</span>
                    </div>
                    <div class="nav-item" title="邀请">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                        </svg>
                        <span class="nav-item-text">邀请</span>
                    </div>
                    <div class="nav-item" title="夜间模式" onclick="toggleNightMode()">
                        <svg class="nav-item-icon" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M9.37 5.51C9.19 6.15 9.1 6.82 9.1 7.5c0 4.08 3.32 7.4 7.4 7.4.68 0 1.35-.09 1.99-.27C17.45 17.19 14.93 19 12 19c-3.86 0-7-3.14-7-7 0-2.93 1.81-5.45 4.37-6.49z"/>
                        </svg>
                        <span class="nav-item-text">夜间</span>
                    </div>
                </div>
            </div>
        </nav>
    </div>

    <!-- 主内容区 -->
    <main class="main-content">
        <!-- 首页容器 -->
        <div id="homePage" class="home-container">
            <div class="hero-section">
                <h1 class="hero-title">笔尖传奇写作</h1>
                <p class="hero-subtitle">选择适合您的创作模式，开启智能写作之旅</p>
                <div class="version-buttons">
                    <div class="version-card" onclick="openSimpleVersion()">
                        <span class="version-icon">🌟</span>
                        <h3 class="version-title">简约版</h3>
                        <p class="version-desc">为新手用户精心设计，简单三步即可开始创作</p>
                        <ul class="version-features">
                            <li>智能引导配置</li>
                            <li>预设提示词模板</li>
                            <li>一键式操作流程</li>
                            <li>新手友好界面</li>
                        </ul>
                    </div>
                    <div class="version-card" onclick="openProVersion()">
                        <span class="version-icon">⚡</span>
                        <h3 class="version-title">专业版</h3>
                        <p class="version-desc">为专业创作者打造，提供完整的创作控制</p>
                        <ul class="version-features">
                            <li>自定义工作流程</li>
                            <li>高级提示词编辑</li>
                            <li>多模型协同创作</li>
                            <li>专业数据管理</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 创作界面容器 (默认隐藏) -->
        <div id="creationInterface" class="creation-interface">
            <div class="creation-panel data-panel">
                <h3 class="panel-title">数据资料</h3>
            </div>
            <div class="creation-panel editor-panel">
                <h3 class="panel-title">编辑区</h3>
            </div>
            <div class="creation-panel ai-panel">
                <h3 class="panel-title">AI工作区</h3>
            </div>
        </div>
    </main>

    <!-- 简约版配置弹窗 -->
    <div class="config-modal" id="simpleConfigModal">
        <div class="config-content">
            <div class="config-header">
                <h2 class="config-title">简约版配置</h2>
                <p class="config-step">请按以下步骤选择您的创作配置</p>
            </div>
            <div class="config-section" id="simpleModelSection">
                <label class="config-label">1. 选择AI模型</label>
                <div class="option-grid">
                    <div class="option-card" data-model="delicate">
                        <div class="option-name">细腻贴合</div>
                        <div class="option-desc">情感细腻，人物刻画生动</div>
                    </div>
                    <div class="option-card" data-model="logical">
                        <div class="option-name">精准逻辑</div>
                        <div class="option-desc">逻辑严密，结构清晰</div>
                    </div>
                    <div class="option-card" data-model="creative">
                        <div class="option-name">灵活创意</div>
                        <div class="option-desc">想象丰富，创意无限</div>
                    </div>
                    <div class="option-card" data-model="stable">
                        <div class="option-name">稳定版</div>
                        <div class="option-desc">输出稳定，质量均衡</div>
                    </div>
                    <div class="option-card" data-model="comprehensive">
                        <div class="option-name">综合版</div>
                        <div class="option-desc">综合能力，全面发展</div>
                    </div>
                    <div class="option-card" data-model="test">
                        <div class="option-name">测试版</div>
                        <div class="option-desc">最新功能，抢先体验</div>
                    </div>
                </div>
            </div>
            <div class="config-section" id="simpleWorkTypeSection" style="display: none;">
                <label class="config-label">2. 选择作品类型</label>
                <div class="option-grid">
                    <div class="option-card" data-type="long">
                        <span class="option-icon">📚</span>
                        <div class="option-name">长篇</div>
                    </div>
                    <div class="option-card" data-type="medium">
                        <span class="option-icon">📖</span>
                        <div class="option-name">中篇</div>
                    </div>
                    <div class="option-card" data-type="short">
                        <span class="option-icon">📄</span>
                        <div class="option-name">短篇</div>
                    </div>
                    <div class="option-card" data-type="script">
                        <span class="option-icon">🎬</span>
                        <div class="option-name">剧本</div>
                    </div>
                </div>
            </div>
            <div class="config-section" id="promptSection" style="display: none;">
                <label class="config-label">3. 选择提示词流程</label>
                <select class="prompt-select" id="promptSelect">
                    <option value="">请选择一个提示词流程...</option>
                </select>
                <div class="tutorial-window" id="tutorialWindow" style="display: none;">
                    <div class="tutorial-title">
                        <span>💡</span>
                        <span id="tutorialTitle">提示词说明</span>
                    </div>
                    <div class="tutorial-content" id="tutorialContent">
                        选择提示词后，这里会显示详细的使用说明和效果介绍。
                    </div>
                </div>
            </div>
            <div class="config-actions">
                <button class="btn btn-secondary" onclick="closeSimpleConfig()">取消</button>
                <button class="btn btn-primary" id="startSimpleBtn" onclick="proceedToCreation('simple')" disabled>开始创作 →</button>
            </div>
        </div>
    </div>
    
    <!-- 专业版配置弹窗 -->
    <div class="config-modal" id="proConfigModal">
        <div class="config-content">
            <div class="config-header">
                <h2 class="config-title">专业版配置</h2>
                <p class="config-step">请定制您的专业创作环境</p>
            </div>
            
            <!-- 步骤 1: 选择模型 -->
            <div class="config-section" id="proModelSection">
                <label class="config-label">1. 选择AI模型</label>
                <div class="option-grid">
                    <div class="option-card" data-model="delicate">
                        <div class="option-name">细腻贴合</div>
                        <div class="option-desc">情感细腻，人物刻画生动</div>
                    </div>
                    <div class="option-card" data-model="logical">
                        <div class="option-name">精准逻辑</div>
                        <div class="option-desc">逻辑严密，结构清晰</div>
                    </div>
                    <div class="option-card" data-model="creative">
                        <div class="option-name">灵活创意</div>
                        <div class="option-desc">想象丰富，创意无限</div>
                    </div>
                    <div class="option-card" data-model="stable">
                        <div class="option-name">稳定版</div>
                        <div class="option-desc">输出稳定，质量均衡</div>
                    </div>
                    <div class="option-card" data-model="comprehensive">
                        <div class="option-name">综合版</div>
                        <div class="option-desc">综合能力，全面发展</div>
                    </div>
                    <div class="option-card" data-model="test">
                        <div class="option-name">测试版</div>
                        <div class="option-desc">最新功能，抢先体验</div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 2: 选择作品类型 -->
            <div class="config-section" id="proWorkTypeSection" style="display: none;">
                <label class="config-label">2. 选择作品类型</label>
                <div class="option-grid">
                    <div class="option-card" data-type="long">
                        <span class="option-icon">📚</span>
                        <div class="option-name">长篇</div>
                    </div>
                    <div class="option-card" data-type="medium">
                        <span class="option-icon">📖</span>
                        <div class="option-name">中篇</div>
                    </div>
                    <div class="option-card" data-type="short">
                        <span class="option-icon">📄</span>
                        <div class="option-name">短篇</div>
                    </div>
                    <div class="option-card" data-type="script">
                        <span class="option-icon">🎬</span>
                        <div class="option-name">剧本</div>
                    </div>
                </div>
            </div>
            
            <!-- 步骤 3: 选择创作模式 -->
            <div class="config-section" id="proCreationModeSection" style="display: none;">
                <label class="config-label">3. 选择创作模式</label>
                <div class="option-grid grid-2-cols">
                    <div class="option-card" data-mode="content">
                        <span class="option-icon">✍️</span>
                        <div class="option-name">内容创作</div>
                        <div class="option-desc">进行正文、大纲等核心内容写作</div>
                    </div>
                    <div class="option-card" data-mode="brainstorm">
                        <span class="option-icon">💡</span>
                        <div class="option-name">脑洞创意</div>
                        <div class="option-desc">用于灵感、设定、角色等构思</div>
                    </div>
                </div>
            </div>

            <!-- 步骤 4: 选择书籍来源 -->
            <div class="config-section" id="proBookSourceSection" style="display: none;">
                <label class="config-label">4. 选择或新建书籍</label>
                <div class="option-grid grid-2-cols">
                    <div class="option-card" data-source="new">
                        <span class="option-icon">➕</span>
                        <div class="option-name">新建书籍</div>
                        <div class="option-desc">从零开始您的全新力作</div>
                    </div>
                    <div class="option-card" data-source="existing">
                        <span class="option-icon">📚</span>
                        <div class="option-name">选择现有书籍</div>
                        <div class="option-desc">从书架中选择已有的书籍项目</div>
                    </div>
                    <div class="option-card" data-source="upload">
                        <span class="option-icon">⬆️</span>
                        <div class="option-name">上传书籍</div>
                        <div class="option-desc">导入现有 .txt 或 .docx 文稿</div>
                    </div>
                </div>
            </div>

            <!-- 用于文件上传的隐藏input -->
            <input type="file" id="fileUploader" accept=".txt,.docx,.md">

            <div class="config-actions">
                <button class="btn btn-secondary" onclick="closeProConfig()">取消</button>
                <button class="btn btn-primary" id="startProBtn" onclick="proceedToCreation('pro')" disabled>进入创作 →</button>
            </div>
        </div>
    </div>

    <!-- 个人中心弹窗 -->
    <div class="user-center-modal" id="userCenterModal">
        <div class="user-center-content">
            <div class="user-center-header">
                <button class="user-center-close" onclick="closeUserCenter()">&times;</button>
                <div class="user-center-avatar">
                    <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="User Avatar" id="userCenterAvatar">
                </div>
                <div class="user-center-name" id="userCenterName">蛙蛙F37qgS</div>
                <div class="user-center-id" id="userCenterExpiry">(9999/12/31)</div>
                <div class="user-center-balance" id="userCenterBalance">无限字数-7天</div>
            </div>
            <div class="user-center-body">
                <button class="user-center-recharge" onclick="openRecharge()">
                    充值或订阅套餐
                </button>
                <div class="user-center-menu">
                    <button class="user-center-menu-item" onclick="openProfile()">
                        <div class="user-center-menu-item-icon">👤</div>
                        <div class="user-center-menu-item-text">个人资料</div>
                        <div class="user-center-menu-item-arrow">›</div>
                    </button>
                    <button class="user-center-menu-item" onclick="openExchange()">
                        <div class="user-center-menu-item-icon">🎫</div>
                        <div class="user-center-menu-item-text">兑换码</div>
                        <div class="user-center-menu-item-arrow">›</div>
                    </button>
                    <button class="user-center-menu-item" onclick="openGuide()">
                        <div class="user-center-menu-item-icon">📖</div>
                        <div class="user-center-menu-item-text">新手引导</div>
                        <div class="user-center-menu-item-arrow">›</div>
                    </button>
                    <button class="user-center-menu-item" onclick="openPromptGifts()">
                        <div class="user-center-menu-item-icon">🎁</div>
                        <div class="user-center-menu-item-text">字数赠送</div>
                        <div class="user-center-menu-item-arrow">›</div>
                    </button>
                    <button class="user-center-menu-item" onclick="openPurchaseHistory()">
                        <div class="user-center-menu-item-icon">📋</div>
                        <div class="user-center-menu-item-text">购买记录</div>
                        <div class="user-center-menu-item-arrow">›</div>
                    </button>
                    <div class="user-center-divider"></div>
                    <button class="user-center-menu-item" onclick="logout()">
                        <div class="user-center-menu-item-icon">🚪</div>
                        <div class="user-center-menu-item-text">退出登录</div>
                        <div class="user-center-menu-item-arrow">›</div>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 充值弹窗 -->
    <div class="recharge-modal" id="rechargeModal">
        <div class="recharge-content">
            <div class="recharge-header">
                <button class="recharge-close" onclick="closeRecharge()">&times;</button>
                <h3 class="recharge-title">💰 充值或订阅套餐</h3>
            </div>
            <div class="recharge-body">
                <div class="recharge-message">
                    充值功能正在开发中...<br>
                    敬请期待更多字数包选择！<br><br>
                    <strong>当前套餐：无限字数-7天</strong>
                </div>
                <div class="recharge-actions">
                    <button class="btn btn-secondary" onclick="closeRecharge()">稍后再说</button>
                    <button class="btn btn-primary" onclick="goToRecharge()">前往充值</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 完整充值界面 -->
    <div class="recharge-full-modal" id="rechargeFullModal">
        <div class="recharge-full-content">
            <div class="recharge-full-header">
                <div class="recharge-full-title">💰 充值或订阅套餐</div>
                <button class="recharge-full-close" onclick="closeRechargeFullModal()">&times;</button>
            </div>
            <div class="recharge-full-body">
                <!-- 套餐类型选择 -->
                <div class="package-type-tabs">
                    <button class="package-type-tab active" data-type="words" onclick="switchPackageType('words')">字数包</button>
                    <button class="package-type-tab" data-type="subscription" onclick="switchPackageType('subscription')">订阅套餐</button>
                </div>

                <!-- 字数包套餐 -->
                <div class="package-grid" id="wordsPackages">
                    <div class="package-card" data-package="1w" onclick="selectPackage('1w')">
                        <div class="package-title">1万字数包</div>
                        <div class="package-price"><span class="currency">¥</span>8.90</div>
                        <div class="package-original-price">万字/0.9元</div>
                        <ul class="package-features">
                            <li>字数包含1万生成字数</li>
                            <li>支持所有AI模型</li>
                            <li>永久有效</li>
                        </ul>
                    </div>
                    <div class="package-card recommended" data-package="10w" onclick="selectPackage('10w')">
                        <div class="package-title">10万字数包</div>
                        <div class="package-price"><span class="currency">¥</span>13.50</div>
                        <div class="package-original-price">万字/1.35元</div>
                        <ul class="package-features">
                            <li>字数包含10万生成字数</li>
                            <li>支持所有AI模型</li>
                            <li>永久有效</li>
                            <li>优先客服支持</li>
                        </ul>
                    </div>
                    <div class="package-card" data-package="50w" onclick="selectPackage('50w')">
                        <div class="package-title">50万字数包</div>
                        <div class="package-price"><span class="currency">¥</span>62.00</div>
                        <div class="package-original-price">万字/1.24元</div>
                        <ul class="package-features">
                            <li>字数包含50万生成字数</li>
                            <li>支持所有AI模型</li>
                            <li>永久有效</li>
                            <li>VIP专属功能</li>
                            <li>专属创作指导</li>
                        </ul>
                    </div>
                </div>

                <!-- 订阅套餐 -->
                <div class="package-grid" id="subscriptionPackages" style="display: none;">
                    <div class="package-card" data-package="week" onclick="selectPackage('week')">
                        <div class="package-title">无限字数-7天</div>
                        <div class="package-price"><span class="currency">¥</span>51.90</div>
                        <div class="package-original-price">原价 ¥68.90</div>
                        <ul class="package-features">
                            <li>7天无限字数使用</li>
                            <li>支持所有AI模型</li>
                            <li>优先客服支持</li>
                        </ul>
                    </div>
                    <div class="package-card recommended" data-package="month" onclick="selectPackage('month')">
                        <div class="package-title">笔尖写作会员</div>
                        <div class="package-price"><span class="currency">¥</span>35.00</div>
                        <div class="package-original-price">月卡/无限字数</div>
                        <ul class="package-features">
                            <li>月卡享15万生成字数</li>
                            <li>支持所有AI模型</li>
                            <li>专属客服通道</li>
                            <li>高级功能优先体验</li>
                        </ul>
                    </div>
                    <div class="package-card" data-package="season" onclick="selectPackage('season')">
                        <div class="package-title">笔尖写作会员</div>
                        <div class="package-price"><span class="currency">¥</span>107.00</div>
                        <div class="package-original-price">季卡/无限字数</div>
                        <ul class="package-features">
                            <li>季卡享50万生成字数</li>
                            <li>支持所有AI模型</li>
                            <li>VIP专属功能</li>
                            <li>优先新功能体验</li>
                            <li>专属创作指导</li>
                        </ul>
                    </div>
                    <div class="package-card" data-package="year" onclick="selectPackage('year')">
                        <div class="package-title">笔尖写作会员</div>
                        <div class="package-price"><span class="currency">¥</span>195.00</div>
                        <div class="package-original-price">年卡/无限字数</div>
                        <ul class="package-features">
                            <li>年卡享251万生成字数</li>
                            <li>支持所有AI模型</li>
                            <li>全部VIP功能</li>
                            <li>一对一创作指导</li>
                            <li>专属作品推广</li>
                            <li>年度创作报告</li>
                        </ul>
                    </div>
                </div>

                <!-- 支付方式 -->
                <div class="payment-section">
                    <div class="payment-title">选择支付方式</div>
                    <div class="payment-methods">
                        <div class="payment-method selected" data-method="wechat" onclick="selectPaymentMethod('wechat')">
                            <div class="payment-method-icon">💚</div>
                            <div class="payment-method-name">微信支付</div>
                        </div>
                        <div class="payment-method" data-method="alipay" onclick="selectPaymentMethod('alipay')">
                            <div class="payment-method-icon">🔵</div>
                            <div class="payment-method-name">支付宝</div>
                        </div>
                        <div class="payment-method" data-method="bank" onclick="selectPaymentMethod('bank')">
                            <div class="payment-method-icon">🏦</div>
                            <div class="payment-method-name">银行卡</div>
                        </div>
                    </div>
                    <div class="recharge-full-actions">
                        <button class="btn btn-secondary" onclick="closeRechargeFullModalAndReturnToUserCenter()">取消</button>
                        <button class="btn btn-primary" onclick="proceedPayment()" id="paymentBtn" disabled>立即支付</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 个人资料界面 -->
    <div class="profile-modal" id="profileModal">
        <div class="profile-content">
            <div class="profile-header">
                <div class="profile-title">👤 个人资料</div>
                <button class="profile-close" onclick="closeProfileModal()">&times;</button>
            </div>
            <div class="profile-body">
                <!-- 头像区域 -->
                <div class="profile-avatar-section">
                    <div class="profile-avatar-large" onclick="changeAvatar()">
                        <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80" alt="用户头像" id="profileAvatarImg">
                    </div>
                    <div class="profile-avatar-change">点击更换头像</div>
                </div>

                <!-- 统计信息 -->
                <div class="profile-stats">
                    <div class="profile-stat">
                        <div class="profile-stat-number" id="profileTotalWords">125,680</div>
                        <div class="profile-stat-label">总字数</div>
                    </div>
                    <div class="profile-stat">
                        <div class="profile-stat-number" id="profileTotalBooks">8</div>
                        <div class="profile-stat-label">作品数</div>
                    </div>
                    <div class="profile-stat">
                        <div class="profile-stat-number" id="profileWritingDays">45</div>
                        <div class="profile-stat-label">创作天数</div>
                    </div>
                </div>

                <!-- 个人信息表单 -->
                <form class="profile-form" onsubmit="saveProfile(event)">
                    <div class="profile-field">
                        <label class="profile-label">用户名</label>
                        <input type="text" class="profile-input" id="profileUsername" value="蛙蛙F37qgS" placeholder="请输入用户名">
                    </div>
                    <div class="profile-field">
                        <label class="profile-label">邮箱</label>
                        <input type="email" class="profile-input" id="profileEmail" value="<EMAIL>" placeholder="请输入邮箱">
                    </div>
                    <div class="profile-field">
                        <label class="profile-label">个人简介</label>
                        <textarea class="profile-input profile-textarea" id="profileBio" placeholder="介绍一下自己吧...">热爱写作的创作者，专注于科幻和奇幻题材的创作。</textarea>
                    </div>
                    <div class="profile-field">
                        <label class="profile-label">创作偏好</label>
                        <select class="profile-input" id="profilePreference">
                            <option value="scifi">科幻小说</option>
                            <option value="fantasy">奇幻小说</option>
                            <option value="romance">言情小说</option>
                            <option value="mystery">悬疑推理</option>
                            <option value="historical">历史小说</option>
                            <option value="urban">都市小说</option>
                        </select>
                    </div>
                    <div class="profile-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeProfileModalAndReturnToUserCenter()">取消</button>
                        <button type="submit" class="btn btn-primary">保存修改</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 新手引导界面 -->
    <div class="guide-modal" id="guideModal">
        <div class="guide-content">
            <div class="guide-header">
                <div class="guide-title">📖 新手引导</div>
                <button class="guide-close" onclick="closeGuideModal()">&times;</button>
            </div>
            <div class="guide-body">
                <div class="guide-steps">
                    <div class="guide-step">
                        <div class="guide-step-number">1</div>
                        <div class="guide-step-content">
                            <div class="guide-step-title">选择创作模式</div>
                            <div class="guide-step-desc">
                                在首页选择"简约版"或"专业版"。简约版适合新手，操作简单；专业版功能更全面，适合有经验的创作者。
                            </div>
                        </div>
                    </div>
                    <div class="guide-step">
                        <div class="guide-step-number">2</div>
                        <div class="guide-step-content">
                            <div class="guide-step-title">配置AI模型</div>
                            <div class="guide-step-desc">
                                根据创作需求选择合适的AI模型：细腻贴合适合情感描写，精准逻辑适合推理小说，灵活创意适合奇幻题材。
                            </div>
                        </div>
                    </div>
                    <div class="guide-step">
                        <div class="guide-step-number">3</div>
                        <div class="guide-step-content">
                            <div class="guide-step-title">选择作品类型</div>
                            <div class="guide-step-desc">
                                确定你要创作的作品类型：长篇、中篇、短篇或剧本。不同类型会有相应的创作流程和提示词模板。
                            </div>
                        </div>
                    </div>
                    <div class="guide-step">
                        <div class="guide-step-number">4</div>
                        <div class="guide-step-content">
                            <div class="guide-step-title">使用提示词</div>
                            <div class="guide-step-desc">
                                选择合适的提示词模板，或者自定义提示词。好的提示词能让AI更好地理解你的创作意图。
                            </div>
                        </div>
                    </div>
                    <div class="guide-step">
                        <div class="guide-step-number">5</div>
                        <div class="guide-step-content">
                            <div class="guide-step-title">开始创作</div>
                            <div class="guide-step-desc">
                                在编辑区输入你的想法，AI会根据你的提示词生成内容。你可以随时修改、完善生成的内容。
                            </div>
                        </div>
                    </div>
                    <div class="guide-step">
                        <div class="guide-step-number">6</div>
                        <div class="guide-step-content">
                            <div class="guide-step-title">保存和管理</div>
                            <div class="guide-step-desc">
                                创作完成后，记得保存作品。你可以在书架中管理所有作品，支持分类、搜索和导出功能。
                            </div>
                        </div>
                    </div>
                </div>
                <div class="guide-actions">
                    <button class="btn btn-secondary" onclick="enableGuideTooltip()">重新启用引导提示</button>
                    <button class="btn btn-secondary" onclick="closeGuideModalAndReturnToUserCenter()">稍后再看</button>
                    <button class="btn btn-primary" onclick="startTutorial()">开始体验</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 提示词赠送界面 -->
    <div class="prompt-gifts-modal" id="promptGiftsModal">
        <div class="prompt-gifts-content">
            <div class="prompt-gifts-header">
                <div class="prompt-gifts-title">🎁 字数赠送记录</div>
                <button class="prompt-gifts-close" onclick="closePromptGiftsModal()">&times;</button>
            </div>
            <div class="prompt-gifts-body">
                <div class="prompt-gifts-list" id="promptGiftsList">
                    <div class="prompt-gifts-item">
                        <div class="prompt-gifts-item-title">新用户注册奖励</div>
                        <div class="prompt-gifts-item-meta">
                            <span class="prompt-gifts-item-date">2024-01-15 14:30</span>
                            <span class="prompt-gifts-item-words">+5,000字</span>
                        </div>
                    </div>
                    <div class="prompt-gifts-item">
                        <div class="prompt-gifts-item-title">连续签到奖励</div>
                        <div class="prompt-gifts-item-meta">
                            <span class="prompt-gifts-item-date">2024-01-14 09:15</span>
                            <span class="prompt-gifts-item-words">+2,000字</span>
                        </div>
                    </div>
                    <div class="prompt-gifts-item">
                        <div class="prompt-gifts-item-title">创作大赛奖励</div>
                        <div class="prompt-gifts-item-meta">
                            <span class="prompt-gifts-item-date">2024-01-10 16:22</span>
                            <span class="prompt-gifts-item-words">+10,000字</span>
                        </div>
                    </div>
                    <div class="prompt-gifts-item">
                        <div class="prompt-gifts-item-title">邀请好友奖励</div>
                        <div class="prompt-gifts-item-meta">
                            <span class="prompt-gifts-item-date">2024-01-08 11:45</span>
                            <span class="prompt-gifts-item-words">+3,000字</span>
                        </div>
                    </div>
                    <div class="prompt-gifts-item">
                        <div class="prompt-gifts-item-title">每日任务完成</div>
                        <div class="prompt-gifts-item-meta">
                            <span class="prompt-gifts-item-date">2024-01-05 20:30</span>
                            <span class="prompt-gifts-item-words">+1,000字</span>
                        </div>
                    </div>
                    <div class="prompt-gifts-item">
                        <div class="prompt-gifts-item-title">作品分享奖励</div>
                        <div class="prompt-gifts-item-meta">
                            <span class="prompt-gifts-item-date">2024-01-03 15:45</span>
                            <span class="prompt-gifts-item-words">+500字</span>
                        </div>
                    </div>
                </div>
                <div class="prompt-gifts-actions">
                    <button class="btn btn-primary" onclick="closePromptGiftsModalAndReturnToUserCenter()">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 购买记录界面 -->
    <div class="purchase-modal" id="purchaseModal">
        <div class="purchase-content">
            <div class="purchase-header">
                <div class="purchase-title">📋 购买记录</div>
                <button class="purchase-close" onclick="closePurchaseModal()">&times;</button>
            </div>
            <div class="purchase-body">
                <div class="purchase-list" id="purchaseList">
                    <div class="purchase-item">
                        <div class="purchase-item-header">
                            <div class="purchase-item-title">无限字数-7天</div>
                            <div class="purchase-item-status success">已完成</div>
                        </div>
                        <div class="purchase-item-details">
                            <div>
                                <div>订单号：BJ202401150001</div>
                                <div>购买时间：2024-01-15 14:30:25</div>
                                <div>支付方式：微信支付</div>
                            </div>
                            <div class="purchase-item-price">¥51.90</div>
                        </div>
                    </div>
                    <div class="purchase-item">
                        <div class="purchase-item-header">
                            <div class="purchase-item-title">无限字数-月卡</div>
                            <div class="purchase-item-status success">已完成</div>
                        </div>
                        <div class="purchase-item-details">
                            <div>
                                <div>订单号：BJ202312280002</div>
                                <div>购买时间：2023-12-28 09:15:42</div>
                                <div>支付方式：支付宝</div>
                            </div>
                            <div class="purchase-item-price">¥189.00</div>
                        </div>
                    </div>
                    <div class="purchase-item">
                        <div class="purchase-item-header">
                            <div class="purchase-item-title">无限字数-7天</div>
                            <div class="purchase-item-status success">已完成</div>
                        </div>
                        <div class="purchase-item-details">
                            <div>
                                <div>订单号：BJ202312150003</div>
                                <div>购买时间：2023-12-15 16:22:18</div>
                                <div>支付方式：微信支付</div>
                            </div>
                            <div class="purchase-item-price">¥51.90</div>
                        </div>
                    </div>
                </div>
                <div class="purchase-actions">
                    <button class="btn btn-secondary" onclick="exportPurchaseHistory()">导出记录</button>
                    <button class="btn btn-primary" onclick="closePurchaseModalAndReturnToUserCenter()">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 兑换码界面 -->
    <div class="exchange-modal" id="exchangeModal">
        <div class="exchange-content">
            <div class="exchange-header">
                <div class="exchange-title">🎫 兑换码</div>
                <button class="exchange-close" onclick="closeExchangeModal()">&times;</button>
            </div>
            <div class="exchange-body">
                <div class="exchange-description">
                    <div class="exchange-desc-title">兑换说明</div>
                    <div class="exchange-desc-text">
                        • 输入有效的兑换码可获得字数包或订阅套餐<br>
                        • 兑换码不区分大小写，支持数字和字母组合<br>
                        • 每个兑换码只能使用一次，请妥善保管<br>
                        • 如有问题请联系客服获取帮助
                    </div>
                </div>

                <form class="exchange-form" onsubmit="submitExchangeCode(event)">
                    <div class="exchange-field">
                        <label class="exchange-label">兑换码</label>
                        <input type="text" class="exchange-input" id="exchangeCodeInput" placeholder="请输入兑换码" maxlength="20">
                        <div class="exchange-input-hint">支持数字和字母，不区分大小写</div>
                    </div>

                    <div class="exchange-actions">
                        <button type="button" class="btn btn-secondary" onclick="closeExchangeModalAndReturnToUserCenter()">取消</button>
                        <button type="submit" class="btn btn-primary" id="exchangeSubmitBtn">立即兑换</button>
                    </div>
                </form>

                <!-- 兑换历史 -->
                <div class="exchange-history">
                    <div class="exchange-history-title">兑换记录</div>
                    <div class="exchange-history-list" id="exchangeHistoryList">
                        <div class="exchange-history-item">
                            <div class="exchange-history-info">
                                <div class="exchange-history-code">BJVIP2024***</div>
                                <div class="exchange-history-reward">笔尖写作会员 - 月卡</div>
                                <div class="exchange-history-time">2024-01-15 14:30</div>
                            </div>
                            <div class="exchange-history-status success">已兑换</div>
                        </div>
                        <div class="exchange-history-item">
                            <div class="exchange-history-info">
                                <div class="exchange-history-code">WORDS10W***</div>
                                <div class="exchange-history-reward">10万字数包</div>
                                <div class="exchange-history-time">2024-01-10 09:15</div>
                            </div>
                            <div class="exchange-history-status success">已兑换</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 书籍选择弹窗 -->
    <div class="book-selector-modal" id="bookSelectorModal">
        <div class="book-selector-content">
            <div class="book-selector-header">
                <h2 class="book-selector-title">选择现有书籍</h2>
                <p class="book-selector-subtitle">从您的书架中选择一个项目继续创作</p>
            </div>
            
            <!-- 搜索框 -->
            <div class="book-search-container">
                <input type="text" class="book-search-input" id="bookSearchInput" placeholder="搜索书籍标题、类型或关键词...">
            </div>
            
            <!-- 书籍列表 -->
            <div class="book-list" id="bookList">
                <!-- 书籍项目将通过JavaScript动态生成 -->
            </div>
            
            <!-- 空状态 -->
            <div class="book-empty-state" id="bookEmptyState" style="display: none;">
                <div class="book-empty-icon">📚</div>
                <div class="book-empty-text">暂无匹配的书籍</div>
                <div class="book-empty-desc">尝试调整搜索关键词或创建新的书籍项目</div>
            </div>
            
            <!-- 按钮组 -->
            <div class="book-selector-actions">
                <button class="btn btn-secondary" onclick="closeBookSelector()">取消</button>
                <button class="btn btn-primary" id="confirmBookBtn" onclick="confirmBookSelection()" disabled>确认选择</button>
            </div>
        </div>
    </div>

    <script>
        // --- 换肤功能 ---
        const savedTheme = localStorage.getItem('theme') || 'default';
        document.documentElement.setAttribute('data-theme', savedTheme);
        
        // 更新换肤按钮状态
        document.querySelectorAll('.theme-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.theme === savedTheme) {
                btn.classList.add('active');
            }
        });
        
        // 换肤按钮点击事件
        document.querySelectorAll('.theme-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const theme = this.dataset.theme;
                document.documentElement.setAttribute('data-theme', theme);
                localStorage.setItem('theme', theme);
                
                document.querySelectorAll('.theme-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 夜间模式快捷切换
        function toggleNightMode() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'default' : 'dark';
            document.documentElement.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            document.querySelectorAll('.theme-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.dataset.theme === newTheme) {
                    btn.classList.add('active');
                }
            });
        }

        // --- 核心逻辑 ---
        let currentSimpleConfig = { model: null, workType: null, promptFlow: null };
        let currentProConfig = { model: null, workType: null, creationMode: null, bookSource: null, uploadedFile: null };
        let selectedBook = null;

        // 模拟书籍数据
        const mockBooks = [
            {
                id: 1,
                title: "星辰大海的征途",
                type: "长篇",
                genre: "科幻",
                wordCount: 125000,
                chapters: 45,
                lastModified: "2024-01-15",
                cover: "🚀",
                description: "一部关于星际探索的史诗级科幻小说"
            },
            {
                id: 2,
                title: "江南烟雨",
                type: "中篇",
                genre: "言情",
                wordCount: 68000,
                chapters: 28,
                lastModified: "2024-01-12",
                cover: "🌸",
                description: "江南水乡的浪漫爱情故事"
            },
            {
                id: 3,
                title: "悬疑档案",
                type: "短篇",
                genre: "悬疑",
                wordCount: 25000,
                chapters: 12,
                lastModified: "2024-01-10",
                cover: "🔍",
                description: "扣人心弦的推理悬疑故事集"
            },
            {
                id: 4,
                title: "修仙传说",
                type: "长篇",
                genre: "玄幻",
                wordCount: 280000,
                chapters: 89,
                lastModified: "2024-01-08",
                cover: "⚔️",
                description: "热血沸腾的修仙冒险之旅"
            },
            {
                id: 5,
                title: "都市风云",
                type: "中篇",
                genre: "都市",
                wordCount: 95000,
                chapters: 35,
                lastModified: "2024-01-05",
                cover: "🏙️",
                description: "现代都市背景的商战小说"
            },
            {
                id: 6,
                title: "时光剧本",
                type: "剧本",
                genre: "剧情",
                wordCount: 45000,
                chapters: 15,
                lastModified: "2024-01-03",
                cover: "🎭",
                description: "穿越时空的戏剧作品"
            }
        ];

        // 示例提示词数据
        const promptTemplates = {
            long: [
                { id: 'fantasy-complete', name: '玄幻小说完整流程', description: '从世界观到正文，一步步引导您创作史诗级玄幻巨著。' },
                { id: 'romance-complete', name: '言情小说创作流程', description: '细腻情感描写，打造令人心动的爱情故事。' }
            ],
            medium: [
                { id: 'suspense-medium', name: '悬疑推理中篇流程', description: '适合构建精巧谜题和意外反转的中篇故事。' },
                { id: 'scifi-medium', name: '科幻中篇创作流程', description: '探索未来世界，创造引人深思的科幻故事。' }
            ],
            short: [
                { id: 'twist-short', name: '反转短篇流程', description: '快速创作一个结局出人意料的精彩短篇。' },
                { id: 'emotion-short', name: '情感短篇流程', description: '用简短篇幅触动人心的情感故事。' }
            ],
            script: [
                { id: 'movie-script', name: '电影剧本标准流程', description: '遵循行业标准的三幕式结构，创作专业剧本。' },
                { id: 'tv-script', name: '电视剧本创作流程', description: '多集结构设计，打造连续性强的电视剧本。' }
            ]
        };

        // --- DOM 元素获取 ---
        const homePage = document.getElementById('homePage');
        const creationInterface = document.getElementById('creationInterface');
        const sidebarWrapper = document.getElementById('sidebarWrapper');
        const sidebarTrigger = document.getElementById('sidebarTrigger');
        
        // 简约版
        const simpleConfigModal = document.getElementById('simpleConfigModal');
        const simpleModelSection = document.getElementById('simpleModelSection');
        const simpleWorkTypeSection = document.getElementById('simpleWorkTypeSection');
        const promptSection = document.getElementById('promptSection');
        const startSimpleBtn = document.getElementById('startSimpleBtn');
        
        // 专业版
        const proConfigModal = document.getElementById('proConfigModal');
        const proModelSection = document.getElementById('proModelSection');
        const proWorkTypeSection = document.getElementById('proWorkTypeSection');
        const proCreationModeSection = document.getElementById('proCreationModeSection');
        const proBookSourceSection = document.getElementById('proBookSourceSection');
        const startProBtn = document.getElementById('startProBtn');
        const fileUploader = document.getElementById('fileUploader');

        // --- 导航栏自动隐藏/显示逻辑 (重写版) ---
        let sidebarTimer;
        
        // 鼠标进入感应区域
        sidebarTrigger.addEventListener('mouseenter', () => {
            clearTimeout(sidebarTimer);
            sidebarWrapper.classList.remove('collapsed');

            if (isGuideActive) {
                showToast('很好！现在点击头像打开个人中心', 'info');
            }
        });
        
        // 鼠标离开感应区域
        sidebarTrigger.addEventListener('mouseleave', (e) => {
            // 检查鼠标是否移到了导航栏上
            const toElement = e.relatedTarget;
            if (toElement && !sidebarWrapper.contains(toElement)) {
                sidebarTimer = setTimeout(() => {
                    sidebarWrapper.classList.add('collapsed');
                }, 300);
            }
        });
        
        // 鼠标在导航栏区域内移动时保持显示
        sidebarWrapper.addEventListener('mouseenter', () => {
            clearTimeout(sidebarTimer);
        });
        
        // 鼠标离开整个导航栏区域
        sidebarWrapper.addEventListener('mouseleave', () => {
            sidebarTimer = setTimeout(() => {
                sidebarWrapper.classList.add('collapsed');

            }, 300);
        });

        // --- 个人中心功能 ---
        // 模拟用户数据
        const userData = {
            name: '南城',
            expiry: '(9999/12/31)',
            balance: '无限字数-7天',
            avatar: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=100&q=80'
        };

        function openUserCenter() {
            // 更新用户信息
            updateUserCenterData();

            const modal = document.getElementById('userCenterModal');
            modal.style.display = 'flex';
            // 添加动画效果
            requestAnimationFrame(() => {
                modal.classList.add('show');
            });
        }

        function updateUserCenterData() {
            document.getElementById('userCenterName').textContent = userData.name;
            document.getElementById('userCenterExpiry').textContent = userData.expiry;
            document.getElementById('userCenterBalance').textContent = userData.balance;
            document.getElementById('userCenterAvatar').src = userData.avatar;
        }

        function closeUserCenter() {
            const modal = document.getElementById('userCenterModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 个人中心菜单功能
        function openRecharge() {
            closeUserCenter();
            // 打开充值弹窗
            const modal = document.getElementById('rechargeModal');
            modal.style.display = 'flex';
            requestAnimationFrame(() => {
                modal.classList.add('show');
            });
        }

        function closeRecharge() {
            const modal = document.getElementById('rechargeModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        function goToRecharge() {
            closeRecharge();
            alert('即将跳转到充值页面...\n\n功能开发中');
        }

        function openProfile() {
            closeUserCenter();
            // 这里可以跳转到个人资料页面
            showToast('个人资料功能开发中...', 'info');
        }



        function openPurchaseHistory() {
            closeUserCenter();
            // 这里可以跳转到购买记录页面
            showToast('购买记录功能开发中...', 'info');
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                closeUserCenter();
                // 这里可以执行登出逻辑
                showToast('退出登录功能开发中...', 'warning');
            }
        }

        // 简单的提示功能 - 左侧边缘显示
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 50%;
                left: 0;
                transform: translateY(-50%);
                background: ${type === 'info' ? 'var(--primary-color)' : 'var(--warning-color)'};
                color: white;
                padding: 12px 20px;
                border-radius: 0 8px 8px 0;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                z-index: 2000;
                font-size: 14px;
                animation: slideInLeft 0.3s ease-out;
                max-width: 300px;
                word-wrap: break-word;
            `;
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.animation = 'slideOutLeft 0.3s ease-out';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 2000);
        }

        // 点击弹窗外部关闭
        document.getElementById('userCenterModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeUserCenter();
            }
        });

        // 点击充值弹窗外部关闭
        document.getElementById('rechargeModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeRecharge();
            }
        });

        // 添加所有弹窗的外部点击关闭事件
        document.getElementById('rechargeFullModal').addEventListener('click', function(e) {
            if (e.target === this) closeRechargeFullModal();
        });
        document.getElementById('profileModal').addEventListener('click', function(e) {
            if (e.target === this) closeProfileModal();
        });
        document.getElementById('guideModal').addEventListener('click', function(e) {
            if (e.target === this) closeGuideModal();
        });
        document.getElementById('purchaseModal').addEventListener('click', function(e) {
            if (e.target === this) closePurchaseModal();
        });
        document.getElementById('promptGiftsModal').addEventListener('click', function(e) {
            if (e.target === this) closePromptGiftsModal();
        });

        // 键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const userCenterModal = document.getElementById('userCenterModal');
                const rechargeModal = document.getElementById('rechargeModal');
                const rechargeFullModal = document.getElementById('rechargeFullModal');
                const profileModal = document.getElementById('profileModal');
                const guideModal = document.getElementById('guideModal');
                const purchaseModal = document.getElementById('purchaseModal');
                const promptGiftsModal = document.getElementById('promptGiftsModal');

                if (rechargeFullModal.classList.contains('show')) {
                    closeRechargeFullModal();
                } else if (profileModal.classList.contains('show')) {
                    closeProfileModal();
                } else if (guideModal.classList.contains('show')) {
                    closeGuideModal();
                } else if (purchaseModal.classList.contains('show')) {
                    closePurchaseModal();
                } else if (promptGiftsModal.classList.contains('show')) {
                    closePromptGiftsModal();
                } else if (rechargeModal.classList.contains('show')) {
                    closeRecharge();
                } else if (userCenterModal.classList.contains('show')) {
                    closeUserCenter();
                }
            }
        });

        // --- 引导系统 ---
        let guideStep = 0;
        let isGuideActive = false;

        function showGuideTooltip() {
            document.getElementById('guideTooltip').classList.add('show');
        }

        function startGuide() {
            isGuideActive = true;
            document.getElementById('guideTooltip').classList.remove('show');
            showToast('请将鼠标悬停在最左侧边缘', 'info');
        }

        function skipGuide() {
            document.getElementById('guideTooltip').classList.remove('show');
            isGuideActive = false;
            // 只是隐藏引导，不记录完成状态，下次还会显示
        }

        function permanentlyCloseGuide() {
            document.getElementById('guideTooltip').classList.remove('show');
            isGuideActive = false;
            // 永久关闭引导
            localStorage.setItem('guidePermanentlyClosed', 'true');
            showToast('引导已永久关闭', 'info');
        }

        function completeGuide() {
            // 立即隐藏所有引导元素
            document.getElementById('guideTooltip').classList.remove('show');
            isGuideActive = false;
            // 只是隐藏引导，不记录完成状态，下次还会显示
            showToast('引导完成！欢迎使用笔尖传奇', 'info');
        }

        // 监听导航栏展开，引导用户点击头像
        const originalOpenUserCenter = openUserCenter;
        openUserCenter = function() {
            if (isGuideActive) {
                completeGuide();
            }
            originalOpenUserCenter();
        };

        // --- 完整充值界面功能 ---
        let selectedPackage = null;
        let selectedPaymentMethod = 'wechat';
        let currentPackageType = 'words';

        function openRechargeFullModal() {
            const modal = document.getElementById('rechargeFullModal');
            modal.style.display = 'flex';
            requestAnimationFrame(() => {
                modal.classList.add('show');
            });
        }

        function closeRechargeFullModal() {
            const modal = document.getElementById('rechargeFullModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        function closeRechargeFullModalAndReturnToUserCenter() {
            const rechargeModal = document.getElementById('rechargeFullModal');
            const userCenterModal = document.getElementById('userCenterModal');

            // 同时开始两个动画
            rechargeModal.classList.remove('show');
            userCenterModal.style.display = 'flex';

            // 延迟一点点让充值弹窗开始退出动画
            setTimeout(() => {
                userCenterModal.classList.add('show');
            }, 150);

            // 清理充值弹窗
            setTimeout(() => {
                rechargeModal.style.display = 'none';
            }, 300);
        }

        function switchPackageType(type) {
            currentPackageType = type;

            // 更新标签状态
            document.querySelectorAll('.package-type-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('active');

            // 切换套餐显示
            document.getElementById('wordsPackages').style.display = type === 'words' ? 'grid' : 'none';
            document.getElementById('subscriptionPackages').style.display = type === 'subscription' ? 'grid' : 'none';

            // 清除选中状态
            selectedPackage = null;
            document.querySelectorAll('.package-card').forEach(card => {
                card.classList.remove('selected');
            });
            updatePaymentButton();
        }

        function selectPackage(packageType) {
            // 移除所有选中状态
            document.querySelectorAll('.package-card').forEach(card => {
                card.classList.remove('selected');
            });
            // 添加选中状态
            document.querySelector(`[data-package="${packageType}"]`).classList.add('selected');
            selectedPackage = packageType;
            updatePaymentButton();
        }

        function selectPaymentMethod(method) {
            // 移除所有选中状态
            document.querySelectorAll('.payment-method').forEach(method => {
                method.classList.remove('selected');
            });
            // 添加选中状态
            document.querySelector(`[data-method="${method}"]`).classList.add('selected');
            selectedPaymentMethod = method;
            updatePaymentButton();
        }

        function updatePaymentButton() {
            const btn = document.getElementById('paymentBtn');
            if (selectedPackage) {
                btn.disabled = false;
            } else {
                btn.disabled = true;
            }
        }

        function proceedPayment() {
            if (!selectedPackage) {
                showToast('请选择一个套餐', 'warning');
                return;
            }
            closeRechargeFullModal();
            showToast('正在跳转到支付页面...', 'info');
            // 这里可以跳转到实际的支付页面
        }

        // 更新充值按钮，打开完整充值界面
        function openRecharge() {
            closeUserCenter();
            openRechargeFullModal();
        }

        // --- 个人资料功能 ---
        function openProfile() {
            closeUserCenter();
            const modal = document.getElementById('profileModal');
            modal.style.display = 'flex';
            requestAnimationFrame(() => {
                modal.classList.add('show');
            });
        }

        function closeProfileModal() {
            const modal = document.getElementById('profileModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        function closeProfileModalAndReturnToUserCenter() {
            const profileModal = document.getElementById('profileModal');
            const userCenterModal = document.getElementById('userCenterModal');

            // 同时开始两个动画
            profileModal.classList.remove('show');
            userCenterModal.style.display = 'flex';

            // 延迟一点点让个人资料开始退出动画
            setTimeout(() => {
                userCenterModal.classList.add('show');
            }, 150);

            // 清理个人资料弹窗
            setTimeout(() => {
                profileModal.style.display = 'none';
            }, 300);
        }

        function changeAvatar() {
            showToast('头像更换功能开发中...', 'info');
        }

        function saveProfile(event) {
            event.preventDefault();
            const username = document.getElementById('profileUsername').value;
            const email = document.getElementById('profileEmail').value;
            const bio = document.getElementById('profileBio').value;
            const preference = document.getElementById('profilePreference').value;

            // 更新用户数据
            userData.name = username;
            updateUserCenterData();

            closeProfileModal();
            showToast('个人资料保存成功！', 'info');
        }

        // --- 新手引导功能 ---
        function openGuide() {
            closeUserCenter();
            const modal = document.getElementById('guideModal');
            modal.style.display = 'flex';
            requestAnimationFrame(() => {
                modal.classList.add('show');
            });
        }

        function closeGuideModal() {
            const modal = document.getElementById('guideModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        function closeGuideModalAndReturnToUserCenter() {
            const guideModal = document.getElementById('guideModal');
            const userCenterModal = document.getElementById('userCenterModal');

            // 同时开始两个动画
            guideModal.classList.remove('show');
            userCenterModal.style.display = 'flex';

            // 延迟一点点让引导弹窗开始退出动画
            setTimeout(() => {
                userCenterModal.classList.add('show');
            }, 150);

            // 清理引导弹窗
            setTimeout(() => {
                guideModal.style.display = 'none';
            }, 300);
        }

        function enableGuideTooltip() {
            // 清除永久关闭标记
            localStorage.removeItem('guidePermanentlyClosed');
            showToast('引导提示已重新启用，下次刷新页面时会显示', 'info');
            // 立即显示引导提示
            setTimeout(() => {
                closeGuideModalAndReturnToUserCenter();
                // 延迟一点显示引导提示
                setTimeout(() => {
                    document.getElementById('guideTooltip').classList.add('show');
                }, 500);
            }, 1000);
        }

        function startTutorial() {
            showToast('开始体验教程...', 'info');
            // 无缝返回个人中心
            setTimeout(() => {
                closeGuideModalAndReturnToUserCenter();
            }, 1000);
            // 这里可以启动交互式教程
        }

        // --- 兑换码功能 ---
        function openExchange() {
            closeUserCenter();
            const modal = document.getElementById('exchangeModal');
            modal.style.display = 'flex';
            setTimeout(() => {
                modal.classList.add('show');
            }, 10);
        }

        function closeExchangeModal() {
            const modal = document.getElementById('exchangeModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        function closeExchangeModalAndReturnToUserCenter() {
            const exchangeModal = document.getElementById('exchangeModal');
            const userCenterModal = document.getElementById('userCenterModal');

            // 同时开始两个动画
            exchangeModal.classList.remove('show');
            userCenterModal.style.display = 'flex';

            // 延迟一点点让兑换弹窗开始退出动画
            setTimeout(() => {
                userCenterModal.classList.add('show');
            }, 150);

            // 清理兑换弹窗
            setTimeout(() => {
                exchangeModal.style.display = 'none';
            }, 300);
        }

        function submitExchangeCode(event) {
            event.preventDefault();
            const codeInput = document.getElementById('exchangeCodeInput');
            const code = codeInput.value.trim().toUpperCase();
            const btn = document.getElementById('exchangeSubmitBtn');

            if (!code) {
                showToast('请输入兑换码', 'warning');
                return;
            }

            if (code.length < 6) {
                showToast('兑换码格式不正确', 'warning');
                return;
            }

            const resetBtn = addLoadingState(btn, '兑换中...');

            // 模拟兑换过程
            setTimeout(() => {
                resetBtn();

                // 模拟兑换结果
                const rewards = [
                    { type: 'words', name: '10万字数包', code: 'WORDS10W' },
                    { type: 'subscription', name: '笔尖写作会员 - 月卡', code: 'BJVIP2024' },
                    { type: 'words', name: '50万字数包', code: 'WORDS50W' },
                    { type: 'subscription', name: '笔尖写作会员 - 季卡', code: 'BJVIP3M' }
                ];

                const randomReward = rewards[Math.floor(Math.random() * rewards.length)];

                if (code.startsWith('BJVIP') || code.startsWith('WORDS') || code === 'TEST123') {
                    showToast(`兑换成功！获得：${randomReward.name}`, 'info');
                    codeInput.value = '';
                    addExchangeHistory(code, randomReward.name);
                } else {
                    showToast('兑换码无效或已使用', 'warning');
                }
            }, 2000);
        }

        function addExchangeHistory(code, reward) {
            const historyList = document.getElementById('exchangeHistoryList');
            const now = new Date();
            const timeStr = now.getFullYear() + '-' +
                          String(now.getMonth() + 1).padStart(2, '0') + '-' +
                          String(now.getDate()).padStart(2, '0') + ' ' +
                          String(now.getHours()).padStart(2, '0') + ':' +
                          String(now.getMinutes()).padStart(2, '0');

            const maskedCode = code.substring(0, Math.min(code.length - 3, 8)) + '***';

            const historyItem = document.createElement('div');
            historyItem.className = 'exchange-history-item';
            historyItem.innerHTML = `
                <div class="exchange-history-info">
                    <div class="exchange-history-code">${maskedCode}</div>
                    <div class="exchange-history-reward">${reward}</div>
                    <div class="exchange-history-time">${timeStr}</div>
                </div>
                <div class="exchange-history-status success">已兑换</div>
            `;

            historyList.insertBefore(historyItem, historyList.firstChild);
        }

        // --- 字数赠送功能 ---
        function openPromptGifts() {
            closeUserCenter();
            const modal = document.getElementById('promptGiftsModal');
            modal.style.display = 'flex';
            requestAnimationFrame(() => {
                modal.classList.add('show');
            });
        }

        function closePromptGiftsModal() {
            const modal = document.getElementById('promptGiftsModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        function closePromptGiftsModalAndReturnToUserCenter() {
            const promptGiftsModal = document.getElementById('promptGiftsModal');
            const userCenterModal = document.getElementById('userCenterModal');

            // 同时开始两个动画
            promptGiftsModal.classList.remove('show');
            userCenterModal.style.display = 'flex';

            // 延迟一点点让字数赠送弹窗开始退出动画
            setTimeout(() => {
                userCenterModal.classList.add('show');
            }, 150);

            // 清理字数赠送弹窗
            setTimeout(() => {
                promptGiftsModal.style.display = 'none';
            }, 300);
        }

        // --- 购买记录功能 ---
        function openPurchaseHistory() {
            closeUserCenter();
            const modal = document.getElementById('purchaseModal');
            modal.style.display = 'flex';
            requestAnimationFrame(() => {
                modal.classList.add('show');
            });
        }

        function closePurchaseModal() {
            const modal = document.getElementById('purchaseModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        function closePurchaseModalAndReturnToUserCenter() {
            const purchaseModal = document.getElementById('purchaseModal');
            const userCenterModal = document.getElementById('userCenterModal');

            // 同时开始两个动画
            purchaseModal.classList.remove('show');
            userCenterModal.style.display = 'flex';

            // 延迟一点点让购买记录开始退出动画
            setTimeout(() => {
                userCenterModal.classList.add('show');
            }, 150);

            // 清理购买记录弹窗
            setTimeout(() => {
                purchaseModal.style.display = 'none';
            }, 300);
        }

        function exportPurchaseHistory() {
            showToast('正在导出购买记录...', 'info');
            // 这里可以实现导出功能
        }

        // --- 工具函数 ---
        function addLoadingState(button, text = '处理中...') {
            const originalText = button.textContent;
            button.disabled = true;
            button.textContent = text;
            return () => {
                button.disabled = false;
                button.textContent = originalText;
            };
        }

        // 通用的取消并返回个人中心函数 - 无缝切换版本
        function cancelAndReturnToUserCenter(modalId) {
            const modal = document.getElementById(modalId);
            const userCenterModal = document.getElementById('userCenterModal');

            // 同时开始两个动画
            modal.classList.remove('show');
            userCenterModal.style.display = 'flex';

            // 延迟一点点让当前弹窗开始退出动画
            setTimeout(() => {
                userCenterModal.classList.add('show');
            }, 150);

            // 清理当前弹窗
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
        }

        // 优化支付按钮
        function proceedPayment() {
            if (!selectedPackage) {
                showToast('请选择一个套餐', 'warning');
                return;
            }
            const btn = document.getElementById('paymentBtn');
            const resetBtn = addLoadingState(btn, '正在跳转...');

            setTimeout(() => {
                resetBtn();
                showToast('支付功能开发中...', 'info');
                // 支付完成后无缝返回个人中心
                setTimeout(() => {
                    closeRechargeFullModalAndReturnToUserCenter();
                }, 1000);
            }, 2000);
        }

        // 优化保存按钮
        function saveProfile(event) {
            event.preventDefault();
            const btn = event.target.querySelector('button[type="submit"]');
            const resetBtn = addLoadingState(btn, '保存中...');

            setTimeout(() => {
                const username = document.getElementById('profileUsername').value;
                const email = document.getElementById('profileEmail').value;
                const bio = document.getElementById('profileBio').value;
                const preference = document.getElementById('profilePreference').value;

                // 更新用户数据
                userData.name = username;
                updateUserCenterData();

                resetBtn();
                showToast('个人资料保存成功！', 'info');

                // 无缝切换回个人中心
                closeProfileModalAndReturnToUserCenter();
            }, 1000);
        }

        // --- 初始化 ---
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否永久关闭了引导
            const guidePermanentlyClosed = localStorage.getItem('guidePermanentlyClosed');

            if (!guidePermanentlyClosed) {
                // 每次页面加载都显示引导（除非用户选择永久关闭）
                setTimeout(() => {
                    document.getElementById('guideTooltip').classList.add('show');
                }, 1500);
            }

            // 添加页面加载完成的淡入效果
            document.body.style.opacity = '0';
            document.body.style.transition = 'opacity 0.5s ease';
            setTimeout(() => {
                document.body.style.opacity = '1';
            }, 100);
        });

        // --- 通用函数 ---
        function proceedToCreation(mode) {
            closeSimpleConfig();
            closeProConfig();
            homePage.style.display = 'none';
            creationInterface.style.display = 'grid';
            
            if (mode === 'simple') {
                console.log('简约版配置:', currentSimpleConfig);
                // 跳转到简约版页面
                window.location.href = '简约版.html?mode=simple';
            } else if (mode === 'pro') {
                console.log('专业版配置:', currentProConfig);
                // 根据创作模式跳转到专业版页面
                const modeParam = currentProConfig.creationMode === 'brainstorm' ? 'brainstorm' : 'content';
                window.location.href = '专业版(3).html?mode=' + modeParam;
            }
        }

        // --- 简约版逻辑 ---
        function openSimpleVersion() { 
            simpleConfigModal.style.display = 'flex'; 
        }
        function closeSimpleConfig() { 
            simpleConfigModal.style.display = 'none'; 
        }
        function checkSimpleConfigComplete() {
            const isComplete = currentSimpleConfig.model && currentSimpleConfig.workType && currentSimpleConfig.promptFlow;
            startSimpleBtn.disabled = !isComplete;
        }
        function updatePromptOptions(workType) {
            const promptSelect = document.getElementById('promptSelect');
            const templates = promptTemplates[workType] || [];
            promptSelect.innerHTML = '<option value="">请选择一个提示词流程...</option>';
            templates.forEach(t => {
                const option = document.createElement('option');
                option.value = t.id;
                option.textContent = t.name;
                option.dataset.description = t.description;
                promptSelect.appendChild(option);
            });
        }
        
        simpleModelSection.querySelectorAll('.option-card').forEach(option => {
            option.addEventListener('click', function() {
                simpleModelSection.querySelectorAll('.option-card').forEach(el => el.classList.remove('selected'));
                this.classList.add('selected');
                currentSimpleConfig.model = this.dataset.model;
                simpleWorkTypeSection.style.display = 'block';
                checkSimpleConfigComplete();
            });
        });
        simpleWorkTypeSection.querySelectorAll('.option-card').forEach(option => {
            option.addEventListener('click', function() {
                simpleWorkTypeSection.querySelectorAll('.option-card').forEach(el => el.classList.remove('selected'));
                this.classList.add('selected');
                currentSimpleConfig.workType = this.dataset.type;
                updatePromptOptions(this.dataset.type);
                promptSection.style.display = 'block';
                checkSimpleConfigComplete();
            });
        });
        document.getElementById('promptSelect').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            currentSimpleConfig.promptFlow = this.value || null;
            const tutorialWindow = document.getElementById('tutorialWindow');
            if (this.value) {
                tutorialWindow.style.display = 'block';
                document.getElementById('tutorialTitle').textContent = selectedOption.textContent;
                document.getElementById('tutorialContent').textContent = selectedOption.dataset.description;
            } else {
                tutorialWindow.style.display = 'none';
            }
            checkSimpleConfigComplete();
        });

        // --- 专业版逻辑 ---
        function openProVersion() {
            proConfigModal.style.display = 'flex';
        }

        function closeProConfig() {
            proConfigModal.style.display = 'none';
            // 重置专业版状态
            currentProConfig = { model: null, workType: null, creationMode: null, bookSource: null, uploadedFile: null };
            proModelSection.querySelectorAll('.option-card').forEach(el => el.classList.remove('selected'));
            proWorkTypeSection.querySelectorAll('.option-card').forEach(el => el.classList.remove('selected'));
            proCreationModeSection.querySelectorAll('.option-card').forEach(el => el.classList.remove('selected'));
            proBookSourceSection.querySelectorAll('.option-card').forEach(el => el.classList.remove('selected'));

            proWorkTypeSection.style.display = 'none';
            proCreationModeSection.style.display = 'none';
            proBookSourceSection.style.display = 'none';
            startProBtn.disabled = true;
        }

        function checkProConfigComplete() {
            const { model, workType, creationMode, bookSource } = currentProConfig;
            // 现在所有创作模式都需要选择书籍来源
            const isComplete = model && workType && creationMode && bookSource;
            startProBtn.disabled = !isComplete;
        }

        // 步骤1: 选择模型
        proModelSection.querySelectorAll('.option-card').forEach(option => {
            option.addEventListener('click', function() {
                proModelSection.querySelectorAll('.option-card').forEach(el => el.classList.remove('selected'));
                this.classList.add('selected');
                currentProConfig.model = this.dataset.model;
                proWorkTypeSection.style.display = 'block';
                checkProConfigComplete();
            });
        });

        // 步骤2: 选择作品类型
        proWorkTypeSection.querySelectorAll('.option-card').forEach(option => {
            option.addEventListener('click', function() {
                proWorkTypeSection.querySelectorAll('.option-card').forEach(el => el.classList.remove('selected'));
                this.classList.add('selected');
                currentProConfig.workType = this.dataset.type;
                proCreationModeSection.style.display = 'block';
                checkProConfigComplete();
            });
        });

        // 步骤3: 选择创作模式
        proCreationModeSection.querySelectorAll('.option-card').forEach(option => {
            option.addEventListener('click', function() {
                proCreationModeSection.querySelectorAll('.option-card').forEach(el => el.classList.remove('selected'));
                this.classList.add('selected');
                currentProConfig.creationMode = this.dataset.mode;

                // 无论选择哪种创作模式，都显示第四步选择书籍
                proBookSourceSection.style.display = 'block';
                currentProConfig.bookSource = null;
                proBookSourceSection.querySelectorAll('.option-card').forEach(el => el.classList.remove('selected'));
                
                checkProConfigComplete();
            });
        });
        
        // 步骤4: 选择书籍来源
        proBookSourceSection.querySelectorAll('.option-card').forEach(option => {
            option.addEventListener('click', function() {
                const source = this.dataset.source;
                if (source === 'upload') {
                    fileUploader.click();
                } else if (source === 'existing') {
                    // 选择现有书籍的逻辑
                    openBookSelector();
                } else {
                    // 新建书籍
                    proBookSourceSection.querySelectorAll('.option-card').forEach(el => el.classList.remove('selected'));
                    this.classList.add('selected');
                    currentProConfig.bookSource = source;
                    currentProConfig.uploadedFile = null;
                    checkProConfigComplete();
                }
            });
        });
        
        // 监听文件上传
        fileUploader.addEventListener('change', function(event) {
            if (event.target.files && event.target.files[0]) {
                const file = event.target.files[0];
                currentProConfig.uploadedFile = file;
                currentProConfig.bookSource = 'upload';

                const uploadCard = proBookSourceSection.querySelector('[data-source="upload"]');
                proBookSourceSection.querySelectorAll('.option-card').forEach(el => el.classList.remove('selected'));
                uploadCard.classList.add('selected');

                alert(`文件已选择：${file.name}`);
                checkProConfigComplete();
            }
            event.target.value = '';
        });

        // --- 书籍选择弹窗功能 ---
        const bookSelectorModal = document.getElementById('bookSelectorModal');
        const bookList = document.getElementById('bookList');
        const bookSearchInput = document.getElementById('bookSearchInput');
        const bookEmptyState = document.getElementById('bookEmptyState');
        const confirmBookBtn = document.getElementById('confirmBookBtn');

        // 打开书籍选择弹窗
        function openBookSelector() {
            bookSelectorModal.style.display = 'flex';
            selectedBook = null;
            confirmBookBtn.disabled = true;
            renderBookList(mockBooks);
            bookSearchInput.value = '';
        }

        // 关闭书籍选择弹窗
        function closeBookSelector() {
            bookSelectorModal.style.display = 'none';
            selectedBook = null;
        }

        // 渲染书籍列表
        function renderBookList(books) {
            if (books.length === 0) {
                bookList.style.display = 'none';
                bookEmptyState.style.display = 'block';
                return;
            }

            bookList.style.display = 'grid';
            bookEmptyState.style.display = 'none';
            
            bookList.innerHTML = books.map(book => `
                <div class="book-item" data-book-id="${book.id}" onclick="selectBook(${book.id})">
                    <div class="book-cover">${book.cover}</div>
                    <div class="book-info">
                        <div class="book-title">${book.title}</div>
                        <div class="book-meta">
                            <div class="book-type">${book.type}</div>
                            <div style="margin-top: 8px; color: var(--text-light);">${book.genre} · ${book.description}</div>
                            <div class="book-stats">
                                <div class="book-stat">
                                    <span>📝</span>
                                    <span>${(book.wordCount / 1000).toFixed(0)}k字</span>
                                </div>
                                <div class="book-stat">
                                    <span>📖</span>
                                    <span>${book.chapters}章</span>
                                </div>
                                <div class="book-stat">
                                    <span>🕒</span>
                                    <span>${book.lastModified}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 选择书籍
        function selectBook(bookId) {
            // 移除之前的选中状态
            document.querySelectorAll('.book-item').forEach(item => {
                item.classList.remove('selected');
            });
            
            // 添加新的选中状态
            const selectedItem = document.querySelector(`[data-book-id="${bookId}"]`);
            if (selectedItem) {
                selectedItem.classList.add('selected');
                selectedBook = mockBooks.find(book => book.id === bookId);
                confirmBookBtn.disabled = false;
            }
        }

        // 确认选择书籍
        function confirmBookSelection() {
            if (selectedBook) {
                // 更新专业版配置
                proBookSourceSection.querySelectorAll('.option-card').forEach(el => el.classList.remove('selected'));
                const existingCard = proBookSourceSection.querySelector('[data-source="existing"]');
                existingCard.classList.add('selected');
                
                currentProConfig.bookSource = 'existing';
                currentProConfig.selectedBook = selectedBook;
                currentProConfig.uploadedFile = null;
                
                // 关闭弹窗
                closeBookSelector();
                
                // 检查配置完成状态
                checkProConfigComplete();
                
                // 显示选择结果
                alert(`已选择书籍：${selectedBook.title}`);
            }
        }

        // 搜索功能
        bookSearchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase().trim();
            
            if (searchTerm === '') {
                renderBookList(mockBooks);
                return;
            }
            
            const filteredBooks = mockBooks.filter(book => {
                return book.title.toLowerCase().includes(searchTerm) ||
                       book.type.toLowerCase().includes(searchTerm) ||
                       book.genre.toLowerCase().includes(searchTerm) ||
                       book.description.toLowerCase().includes(searchTerm);
            });
            
            renderBookList(filteredBooks);
        });

        // 点击弹窗外部关闭
        bookSelectorModal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeBookSelector();
            }
        });

    </script>
</body>
</html>
